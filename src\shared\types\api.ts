/**
 * Tipos genéricos para respuestas de API
 * Define las interfaces reutilizables para respuestas paginadas de Strapi
 */

/**
 * Metadatos de paginación de Strapi
 */
export interface PaginationMeta {
    page: number
    pageSize: number
    pageCount: number
    total: number
}

/**
 * Estructura de metadatos completa de Strapi
 */
export interface ApiMeta {
    pagination: PaginationMeta
    total: number
}

/**
 * Respuesta paginada genérica de Strapi
 * @template T - Tipo de los elementos en el array de datos
 */
export interface PaginatedResponse<T> {
    data: T[]
    meta: ApiMeta
}

/**
 * Respuesta completa de la API incluyendo metadatos HTTP
 * @template T - Tipo de los datos de respuesta
 */
export interface ApiResponse<T> {
    data: T
    status: number
    statusText: string
    headers: Record<string, string>
    config: Record<string, unknown>
    request?: unknown
}

/**
 * Tipo de utilidad para extraer datos de una respuesta paginada
 * @template T - Tipo de los elementos en el array
 */
export type ExtractPaginatedData<T> = T extends PaginatedResponse<infer U> ? U[] : never

/**
 * Tipo de utilidad para extraer metadatos de paginación
 */
export type ExtractPaginationMeta<T> = T extends PaginatedResponse<unknown> ? ApiMeta : never