import type { Department, DepartmentStats } from '../types'
import type { PaginatedResponse } from '@/shared/types/api'

/**
 * Contrato que deben implementar los servicios de departamentos.
 * Permite alternar fácilmente entre la API real y los mocks.
 */
export interface IDepartmentsService {
    /** Obtiene todos los departamentos con paginación */
    getDepartments(): Promise<PaginatedResponse<Department>>
    
    /** Obtiene solo los departamentos activos con paginación */
    getActiveDepartments(): Promise<PaginatedResponse<Department>>
    
    /**
     * Obtiene un departamento por su identificador
     * @param id - ID del departamento
     */
    getDepartmentById(id: string | number): Promise<Department | null>
    
    /**
     * Obtiene estadísticas de un departamento
     * @param id - ID del departamento
     */
    getDepartmentStats(id: string | number): Promise<DepartmentStats>
}