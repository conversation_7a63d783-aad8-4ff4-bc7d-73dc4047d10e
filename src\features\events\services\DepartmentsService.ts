import appConfig from '@/shared/configs/app.config'
import type { IDepartmentsService } from './DepartmentsService.interface'
import DepartmentsApiService from './DepartmentsApiService'
import DepartmentsMockService from './DepartmentsMockService'

/**
 * Servicio principal de departamentos.
 * Dependiendo de la configuración específica de eventos devuelve la implementación real o la simulada.
 * Usa enableEventsMock para permitir control granular por módulo.
 */
const DepartmentsService: IDepartmentsService = appConfig.enableEventsMock
    ? DepartmentsMockService
    : DepartmentsApiService

export default DepartmentsService