import { formatDate, formatTime } from '@/shared/utils/formatEventDate'

interface EventTimeInfoProps {
    startDate: string
    startTime: string
    endDate?: string
    endTime?: string
}

/**
 * Muestra la información de horario de un evento teniendo en cuenta eventos de un día o multi-día
 */
const EventTimeInfo = ({ startDate, startTime, endDate, endTime }: EventTimeInfoProps) => {
    const isMultiDay = Boolean(endDate && endDate !== startDate)

    if (isMultiDay) {
        return (
            <span className="flex flex-col">
                <span>
                    Inicio: {formatDate(startDate)} {formatTime(startTime)}
                </span>
                {endDate && endTime && (
                    <span>
                        Fin: {formatDate(endDate)} {formatTime(endTime)}
                    </span>
                )}
            </span>
        )
    }

    return <span>{`${formatTime(startTime)}${endTime ? ` - ${formatTime(endTime)}` : ''}`}</span>
}

export default EventTimeInfo
