import { Table, Button, Input, DatePicker, Select } from '@/shared/components/ui'
import { HiOutlineTrash, HiPlus } from 'react-icons/hi'
import type { AttendanceSession } from '../../types/attendanceSession'
import type { AttendanceSessionStatus } from '../../types'
import SessionStatusBadge from '../SessionStatusBadge'
import { canChangeSessionStatus } from '../../utils/eventStatusUtils'

interface SessionTableProps {
    sessions: AttendanceSession[]
    onUpdateSession: (idx: number, session: AttendanceSession) => void
    onAddSession: () => void
    onDeleteSession: (idx: number) => void
    eventDates: { start: string; end?: string }
}

const attendanceModeOptions = [
    { value: 'normal', label: 'Normal' },
    { value: 'kiosk', label: 'Kios<PERSON>' },
]

const sessionStatusOptions = [
    { value: 'pendiente', label: 'Pendiente' },
    { value: 'en_progreso', label: 'En Progreso' },
    { value: 'completada', label: 'Completada' },
    { value: 'cancelada', label: 'Cancelada' },
]

const { THead, TBody, Tr, Th, Td } = Table

const SessionTable = ({
    sessions,
    onUpdateSession,
    onAddSession,
    onDeleteSession,
    eventDates,
}: SessionTableProps) => {
    const handleChange = (
        idx: number,
        field: keyof AttendanceSession,
        value: string | boolean,
    ) => {
        const updated = { ...sessions[idx], [field]: value }
        onUpdateSession(idx, updated)
    }

    return (
        <div>
            <Table className="min-w-full">
                <THead>
                    <Tr>
                        <Th>Fecha</Th>
                        <Th>Comentario</Th>
                        <Th>Modo</Th>
                        <Th>Estado</Th>
                        <Th></Th>
                    </Tr>
                </THead>
                <TBody>
                    {sessions.map((session, idx) => (
                        <Tr key={session.id || idx}>
                            <Td>
                                <DatePicker
                                    placeholder="Seleccionar fecha"
                                    value={session.date ? new Date(session.date + 'T00:00:00') : null}
                                    onChange={(date) => {
                                        if (date) {
                                            const formattedDate = date.toISOString().split('T')[0]
                                            handleChange(idx, 'date', formattedDate)
                                        }
                                    }}
                                />
                            </Td>
                            <Td>
                                <div className="flex items-center gap-2">
                                    <Input
                                        value={session.comment || ''}
                                        onChange={(e) =>
                                            handleChange(idx, 'comment', e.target.value)
                                        }
                                    />
                                    {session.isDefault && (
                                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full whitespace-nowrap">
                                            Por defecto
                                        </span>
                                    )}
                                </div>
                            </Td>
                            <Td>
                                <Select
                                    options={attendanceModeOptions}
                                    value={attendanceModeOptions.find(
                                        (o) => o.value === session.attendanceMode,
                                    )}
                                    onChange={(opt) =>
                                        opt &&
                                        handleChange(
                                            idx,
                                            'attendanceMode',
                                            opt.value as 'normal' | 'kiosk',
                                        )
                                    }
                                />
                            </Td>
                            <Td>
                                <div className="flex items-center gap-2">
                                    <SessionStatusBadge
                                        status={session.status}
                                        size="sm"
                                    />
                                    <Select
                                        size="sm"
                                        options={sessionStatusOptions}
                                        value={sessionStatusOptions.find(
                                            (o) => o.value === session.status,
                                        )}
                                        onChange={(opt) =>
                                            opt &&
                                            handleChange(
                                                idx,
                                                'status',
                                                opt.value as AttendanceSessionStatus,
                                            )
                                        }
                                        placeholder="Cambiar estado"
                                    />
                                </div>
                            </Td>
                            <Td className="w-1">
                                {!session.isDefault && (
                                    <Button
                                        size="sm"
                                        variant="plain"
                                        icon={<HiOutlineTrash />}
                                        onClick={() => onDeleteSession(idx)}
                                        type="button"
                                    />
                                )}
                            </Td>
                        </Tr>
                    ))}
                </TBody>
            </Table>
            <div className="mt-2">
                <Button
                    size="sm"
                    variant="twoTone"
                    icon={<HiPlus />}
                    onClick={onAddSession}
                    type="button"
                >
                    Añadir Sesión
                </Button>
            </div>
        </div>
    )
}

export default SessionTable
