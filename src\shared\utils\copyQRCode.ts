import toast from '@/shared/components/ui/toast'

/**
 * Copia un código QR al portapapeles como imagen PNG
 * @param qrElement - Elemento SVG del código QR que se va a copiar
 * @param successMessage - Mensaje de éxito personalizado (opcional)
 * @param errorMessage - Mensaje de error personalizado (opcional)
 * @returns Promise<boolean> - true si la copia fue exitosa, false en caso contrario
 */
const copyQRCode = async (
    qrElement: SVGSVGElement | null,
    successMessage: string = 'Código QR copiado al portapapeles',
    errorMessage: string = 'Error al copiar el código QR'
): Promise<boolean> => {
    if (!qrElement) {
        toast.push({
            title: 'Error',
            message: 'No se encontró el código QR',
            type: 'danger'
        })
        return false
    }

    try {
        // Verificar si el navegador soporta la API de portapapeles
        if (!navigator.clipboard || !navigator.clipboard.write) {
            toast.push({
                title: 'Error',
                message: 'Tu navegador no soporta copiar imágenes al portapapeles',
                type: 'danger'
            })
            return false
        }

        // Obtener las dimensiones del SVG
        const svgData = new XMLSerializer().serializeToString(qrElement)
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
        const svgUrl = URL.createObjectURL(svgBlob)

        // Crear una imagen para cargar el SVG
        const img = new Image()
        
        return new Promise((resolve) => {
            img.onload = async () => {
                try {
                    // Crear un canvas para convertir el SVG a PNG
                    const canvas = document.createElement('canvas')
                    const ctx = canvas.getContext('2d')
                    
                    if (!ctx) {
                        throw new Error('No se pudo crear el contexto del canvas')
                    }

                    // Establecer las dimensiones del canvas
                    canvas.width = img.width
                    canvas.height = img.height

                    // Dibujar la imagen en el canvas
                    ctx.drawImage(img, 0, 0)

                    // Convertir el canvas a blob PNG
                    canvas.toBlob(async (blob) => {
                        if (!blob) {
                            toast.push({
                                title: 'Error',
                                message: errorMessage,
                                type: 'danger'
                            })
                            resolve(false)
                            return
                        }

                        try {
                            // Copiar la imagen al portapapeles
                            await navigator.clipboard.write([
                                new ClipboardItem({ 'image/png': blob })
                            ])

                            toast.push({
                                title: 'Éxito',
                                message: successMessage,
                                type: 'success'
                            })
                            resolve(true)
                        } catch (clipboardError) {
                            console.error('Error al copiar al portapapeles:', clipboardError)
                            toast.push({
                                title: 'Error',
                                message: errorMessage,
                                type: 'danger'
                            })
                            resolve(false)
                        } finally {
                            // Limpiar recursos
                            URL.revokeObjectURL(svgUrl)
                        }
                    }, 'image/png')
                } catch (canvasError) {
                    console.error('Error al procesar el canvas:', canvasError)
                    toast.push({
                        title: 'Error',
                        message: errorMessage,
                        type: 'danger'
                    })
                    URL.revokeObjectURL(svgUrl)
                    resolve(false)
                }
            }

            img.onerror = () => {
                console.error('Error al cargar la imagen SVG')
                toast.push({
                    title: 'Error',
                    message: errorMessage,
                    type: 'danger'
                })
                URL.revokeObjectURL(svgUrl)
                resolve(false)
            }

            img.src = svgUrl
        })
    } catch (error) {
        console.error('Error general al copiar QR:', error)
        toast.push({
                title: 'Error',
                message: errorMessage,
                type: 'danger'
            })
        return false
    }
}

export default copyQRCode