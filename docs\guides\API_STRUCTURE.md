# Estructura de la API

Este documento describe la estructura de configuración y endpoints de la API utilizada en el proyecto.

## Configuración de la API

La configuración de la API se encuentra en el directorio `src/shared/configs/api.config/` y consta de un archivo principal:

### `index.ts`

Este archivo contiene la configuración general para las peticiones a la API:

```typescript
/**
 * Configuración para las peticiones a la API
 * Utiliza las variables de entorno para configurar la URL base y el timeout
 */
export const apiConfig = {
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:1337',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '60000'),
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
    apiPrefix: '/api',
} as const
```

También incluye configuración específica para la autenticación:

```typescript
/**
 * Configuración específica para la autenticación con la API
 * Utiliza las constantes existentes del proyecto
 */
export const apiAuthConfig = {
    tokenType: 'Bearer',
    authHeaderKey: 'Authorization',
    unauthorizedStatusCode: [401, 403],
} as const
```

## Endpoints de la API

Los endpoints de la API se encuentran en el archivo `src/shared/constants/api.constant.ts` y están organizados por módulos:

### Migración de MEETINGS a EVENTS

El módulo de reuniones ha evolucionado a eventos para ser más general. Los endpoints de `MEETINGS` están marcados como deprecados y se debe usar `EVENTS` en su lugar.

```typescript
export const API_ENDPOINTS = {
    AUTH: {
        SIGN_IN: `${apiConfig.apiPrefix}/auth/local`,
        SIGN_UP: `${apiConfig.apiPrefix}/auth/local/register`,
        SIGN_OUT: `${apiConfig.apiPrefix}/auth/logout`,
        ME: `${apiConfig.apiPrefix}/users/me`,
        FORGOT_PASSWORD: `${apiConfig.apiPrefix}/auth/forgot-password`,
        RESET_PASSWORD: `${apiConfig.apiPrefix}/auth/reset-password`,
    },
    USERS: {
        BASE: `${apiConfig.apiPrefix}/users`,
        BY_ID: (id: string) => `${apiConfig.apiPrefix}/users/${id}`,
        ME_WITH_ROLE: `${apiConfig.apiPrefix}/users/me?populate=role`,
    },
    ROLES: {
        BASE: `${apiConfig.apiPrefix}/users-permissions/roles`,
        BY_ID: (id: number) => `${apiConfig.apiPrefix}/users-permissions/roles/${id}`,
        WITH_PERMISSIONS: (id: number) => `${apiConfig.apiPrefix}/users-permissions/roles/${id}`,
    },
    PERMISSIONS: {
        BY_ROLE: (roleId: number) => `${apiConfig.apiPrefix}/users-permissions/roles/${roleId}/permissions`,
    },
} as const
```

## Servicios

Los servicios utilizan estos endpoints para realizar peticiones a la API:

### `BaseService.ts`

Este servicio configura Axios con la configuración de la API y maneja los interceptores para la autenticación y los errores.

### `AuthService.ts`

Este servicio maneja las operaciones relacionadas con la autenticación, como iniciar sesión, registrarse, cerrar sesión, etc.

### `UserService.ts`

Este servicio maneja las operaciones relacionadas con los usuarios y roles, como obtener usuarios, roles, permisos, etc.

## Uso

Para utilizar los endpoints en un servicio:

```typescript
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import ApiService from '@/shared/services/ApiService'

// Ejemplo de uso
const getUserData = () => {
    return ApiService.fetchData({
        url: API_ENDPOINTS.USERS.ME_WITH_ROLE,
        method: 'get',
    })
}
```

## Ventajas de esta estructura

1. **Organización**: Los endpoints están organizados por módulos, lo que facilita su mantenimiento.
2. **Tipado**: El uso de `as const` garantiza que los endpoints sean de solo lectura.
3. **Centralización**: Toda la configuración de la API está centralizada en un solo lugar.
4. **Funciones para endpoints dinámicos**: Los endpoints que requieren parámetros están implementados como funciones.
