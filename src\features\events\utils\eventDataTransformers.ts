import { Event, AttendanceSession } from '../types';

/**
 * Convierte un tiempo en formato HH:mm o timestamp ISO al formato HH:mm:ss.SSS que espera Strapi
 * @param timeString - Tiempo en formato HH:mm (ej: "14:30") o timestamp ISO (ej: "2025-07-24T02:00:00.000Z")
 * @returns Tiempo en formato HH:mm:ss.SSS (ej: "14:30:00.000")
 */
function formatTimeForStrapi(timeString: any): string {
  // Validar que el parámetro sea una cadena de texto válida
  if (!timeString || typeof timeString !== 'string') {
    return '';
  }
  
  // Si es un timestamp ISO (contiene 'T'), extraer solo la parte de tiempo
  if (timeString.includes('T')) {
    try {
      const date = new Date(timeString);
      const hours = date.getUTCHours().toString().padStart(2, '0');
      const minutes = date.getUTCMinutes().toString().padStart(2, '0');
      const seconds = date.getUTCSeconds().toString().padStart(2, '0');
      const milliseconds = date.getUTCMilliseconds().toString().padStart(3, '0');
      return `${hours}:${minutes}:${seconds}.${milliseconds}`;
    } catch (error) {
      console.error('Error al parsear timestamp ISO:', error);
      return '';
    }
  }
  
  // Si ya tiene el formato correcto (HH:mm:ss.SSS), devolverlo tal como está
  if (timeString.includes(':') && timeString.split(':').length >= 3) {
    return timeString;
  }
  
  // Si está en formato HH:mm, agregar segundos y milisegundos
  if (timeString.includes(':') && timeString.split(':').length === 2) {
    return `${timeString}:00.000`;
  }
  
  // Si no tiene formato válido, devolver cadena vacía
  return '';
}

/**
 * Interfaz que define la estructura de datos que espera Strapi para crear/actualizar eventos
 */
export interface StrapiEventData {
  // id: string;
  // documentId: string;
  title: string;
  subject: string;
  topic: string;
  description: string;
  location: string;
  date: string;
  endDate?: string;
  startTime: string;
  endTime: string;
  isMultiDay: boolean;
  hasMultipleSessions: boolean;
  autoRegistration: boolean;
  attendanceMethod: string; // Transformado desde attendanceMode
  sendNotifications: boolean;
  eventStatus: string;
  invitingDepartment: string; // Puede ser string o ID
  participants_invited: string[]; // Transformado desde participantsInvited
  eventType: string | number; // Transformado desde type
  sessions: StrapiSessionData[];
  attendanceRecords?: string[];
  locale?: string;
  localizations?: string[];
}

/**
 * Interfaz para las sesiones en el formato de Strapi
 */
export interface StrapiSessionData {
  id?: number;
  sessionId?: number;
  date: string;
  comment: string;
  attendanceMode: string;
  isDefault: boolean;
  status: string;
}

/**
 * Transforma los datos del evento desde el formato del frontend al formato que espera Strapi
 * @param eventData - Datos del evento en formato del frontend
 * @returns Datos transformados para enviar a Strapi
 */
export function transformEventDataForStrapi(eventData: any): StrapiEventData {
  // Transformar las sesiones al formato de Strapi
  const transformedSessions: StrapiSessionData[] = eventData.sessions?.map((session: AttendanceSession, index: number) => ({
    // id: session.id === 'default_session' ? undefined : Number(session.id),
    sessionId: index + 1, // Generar sessionId secuencial: 1, 2, 3, etc.
    date: session.date,
    comment: session.comment || '',
    attendanceMode: session.attendanceMode || 'normal',
    isDefault: session.isDefault || false,
    status: session.status || 'pendiente'
  })) || [];

  // Extraer el ID del tipo de evento si es un objeto
  let eventTypeId: string | number;
  if (typeof eventData.eventType === 'object' && eventData.eventType !== null) {
    eventTypeId = eventData.eventType.id || eventData.eventType.documentId || '';
  } else {
    eventTypeId = eventData.eventType || '';
  }

  // Transformar participantes invitados (extraer IDs si son objetos)
  const participantsInvited = eventData.participantsInvited?.map((participant: any) => {
    if (typeof participant === 'object' && participant !== null) {
      return participant.id || participant.documentId || '';
    }
    return participant;
  }) || [];

  // Construir el objeto transformado
  const transformedData: StrapiEventData = {
    // id: eventData.id || '',
    // documentId:  eventData.documentId || '',
    title: eventData.title || '',
    subject: eventData.subject || '',
    topic: eventData.topic || '',
    description: eventData.description || '',
    location: eventData.location || '',
    date: eventData.date || '',
    endDate: eventData.endDate || undefined,
    startTime: formatTimeForStrapi(eventData.startTime || ''),
    endTime: formatTimeForStrapi(eventData.endTime || ''),
    isMultiDay: eventData.isMultiDay || false,
    hasMultipleSessions: eventData.hasMultipleSessions || false,
    autoRegistration: eventData.autoRegistration || false,
    attendanceMethod: eventData.attendanceMode || 'manual', // Transformación clave: attendanceMode -> attendanceMethod
    sendNotifications: eventData.sendNotifications || false,
    eventStatus: eventData.eventStatus || 'programada',
    invitingDepartment: eventData.invitingDepartment || '', // El campo invitingDepartment ahora contiene el ID del departamento
    participants_invited: participantsInvited, // Transformación clave: participantsInvited -> participants_invited
    eventType: eventTypeId, // Transformación clave: eventType -> eventType (ya está correcto)
    sessions: transformedSessions
  };

  // Agregar campos opcionales solo si están presentes
  if (eventData.attendanceRecords) {
    transformedData.attendanceRecords = eventData.attendanceRecords;
  }

  if (eventData.locale) {
    transformedData.locale = eventData.locale;
  }

  if (eventData.localizations) {
    transformedData.localizations = eventData.localizations;
  }

  return transformedData;
}

/**
 * Valida que los datos transformados tengan los campos requeridos
 * @param data - Datos transformados para validar
 * @returns true si los datos son válidos, false en caso contrario
 */
export function validateStrapiEventData(data: StrapiEventData): boolean {
  const requiredFields = [
    'title',
    'subject',
    'topic',
    'location',
    'date',
    'startTime',
    'endTime',
    'attendanceMethod',
    'status'
  ];

  return requiredFields.every(field => {
    const value = data[field as keyof StrapiEventData];
    return value !== undefined && value !== null && value !== '';
  });
}

/**
 * Transforma los datos del evento desde el formato de Strapi al formato del frontend
 * @param strapiData - Datos del evento en formato de Strapi
 * @returns Datos transformados para el frontend
 */
export function transformEventDataFromStrapi(strapiData: any): Event {
  // Asegurar que tenemos tanto id como documentId
  const eventData: Event = {
    id: strapiData.id || strapiData.documentId,
    documentId: strapiData.documentId || `doc-${strapiData.id}`,
    title: strapiData.title || '',
    subject: strapiData.subject || '',
    topic: strapiData.topic || '',
    description: strapiData.description || '',
    location: strapiData.location || '',
    date: strapiData.date || '',
    endDate: strapiData.endDate || undefined,
    startTime: strapiData.startTime || '',
    endTime: strapiData.endTime || '',
    isMultiDay: strapiData.isMultiDay || false,
    hasMultipleSessions: strapiData.hasMultipleSessions || false,
    autoRegistration: strapiData.autoRegistration || false,
    sendNotifications: strapiData.sendNotifications || false,
    eventStatus: strapiData.eventStatus || 'programada',
    invitingDepartment: strapiData.invitingDepartment || '',
    eventType: strapiData.eventType || undefined,
    participantsInvited: strapiData.participants_invited || strapiData.participantsInvited || [],
    sessions: strapiData.sessions || [],
    attendanceRecords: strapiData.attendanceRecords || [],
    attendanceMethod: strapiData.attendanceMethod || 'manual',
  };

  return eventData;
}

/**
 * Función de utilidad para logging de la transformación (útil para debugging)
 * @param originalData - Datos originales
 * @param transformedData - Datos transformados
 */
export function logTransformation(originalData: any, transformedData: StrapiEventData): void {
  console.group('🔄 Transformación de datos del evento');
  console.log('📥 Datos originales:', originalData);
  console.log('📤 Datos transformados:', transformedData);
  console.log('✅ Validación:', validateStrapiEventData(transformedData) ? 'Válido' : 'Inválido');
  console.groupEnd();
}