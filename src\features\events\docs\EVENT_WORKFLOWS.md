# Flujos del Módulo de Eventos

Este documento consolida la información sobre la navegación y el manejo de estados dentro de la funcionalidad de eventos.

## Navegación

Las vistas de asistencia utilizan el hook `useNavigationContext` para recordar la pantalla de origen y permitir volver de forma consistente. Ejemplos:

- Desde **Registrar Asistencia** en el dashboard → regreso a `/attendance`.
- Desde la lista de eventos → regreso a `/events/list`.
- Desde los detalles de un evento → regreso a `/events/{id}`.

El modal de selección de sesiones se amplió a 800px para mejorar la usabilidad y evitar scroll horizontal.

## Estados de Sesión y Evento

Las sesiones pueden estar en estado `pendiente`, `en_progreso`, `completada` o `cancelada`. Un evento se marca como `completado` solo cuando todas sus sesiones están completadas.

Transiciones principales:

- `pendiente` → `en_progreso` o `cancelada`.
- `en_progreso` → `completada` o `cancelada`.
- `completada` → `en_progreso` si se reabre.
- `cancelada` → `pendiente` o `en_progreso`.

El progreso del evento se determina evaluando el estado de sus sesiones y se muestra mediante badges y mensajes informativos.

## Registro de Asistencia

1. **Modo manual** (`AttendanceView`): un encargado marca la asistencia de cada participante. Puede guardar progreso parcial y finalizar la sesión cuando esté completa.
2. **Modo kiosko** (`FastAttendanceView`): los participantes se registran por sí mismos. Al finalizar, se marcan automáticamente como ausentes quienes no registraron su asistencia.

## Recomendaciones

- Validar con `eventValidations.ts` antes de cambiar estados o completar sesiones.
- Utilizar `exportEventParticipants.tsx` para generar un Excel con la información del evento y sus asistentes.
