import { Badge } from '@/shared/components/ui'
import type { AttendanceSessionStatus } from '../../types'
import { getSessionStatusMessage } from '../../utils/eventStatusUtils'

interface SessionStatusBadgeProps {
    status: AttendanceSessionStatus
    showText?: boolean
    size?: 'sm' | 'md' | 'lg'
    className?: string
}

/**
 * Componente reutilizable para mostrar el estado de una sesión de asistencia
 * con colores y estilos consistentes en toda la aplicación
 */
const SessionStatusBadge = ({ 
    status, 
    showText = true, 
    size = 'md',
    className = '' 
}: SessionStatusBadgeProps) => {
    
    // Configuración de colores y estilos para cada estado
    const statusConfig = {
        'pendiente': {
            color: 'gray' as const,
            variant: 'solid' as const,
            text: 'Pendiente',
            icon: '⏳'
        },
        'en_progreso': {
            color: 'blue' as const,
            variant: 'solid' as const,
            text: 'En Progreso',
            icon: '🔄'
        },
        'completada': {
            color: 'green' as const,
            variant: 'solid' as const,
            text: 'Completada',
            icon: '✅'
        },
        'cancelada': {
            color: 'red' as const,
            variant: 'solid' as const,
            text: 'Cancelada',
            icon: '❌'
        }
    }

    const config = statusConfig[status]
    
    if (!config) {
        return (
            <Badge 
                color="gray" 
                variant="solid" 
                className={className}
            >
                Estado desconocido
            </Badge>
        )
    }

    const displayText = showText ? config.text : config.icon

    return (
        <Badge 
            color={config.color}
            variant={config.variant}
            className={`${className} ${getSizeClasses(size)}`}
            title={getSessionStatusMessage(status)}
        >
            {!showText && <span className="mr-1">{config.icon}</span>}
            {displayText}
        </Badge>
    )
}

/**
 * Obtiene las clases CSS para el tamaño del badge
 */
function getSizeClasses(size: 'sm' | 'md' | 'lg'): string {
    switch (size) {
        case 'sm':
            return 'text-xs px-2 py-1'
        case 'lg':
            return 'text-sm px-3 py-2'
        case 'md':
        default:
            return 'text-xs px-2.5 py-1.5'
    }
}

export default SessionStatusBadge
