import type {
    Event,
    EventType,
    EventParticipant,
    HierarchicalFilterData,
    AttendanceRecord,
    EventStatus,
    AttendanceSessionStatus,
    ParticipantValidationResult,
    ParticipantValidationData,
} from '../types'
import type { PaginatedResponse } from '@/shared/types/api'

/**
 * Contrato que deben implementar los servicios de eventos.
 * Permite alternar fcilmente entre la API real y los mocks.
 */
export interface IEventsService {
    getEvents(): Promise<PaginatedResponse<Event>>
    getEventById(id: string | number): Promise<Event | null>
    createEvent(eventData: Omit<Event, 'id'>): Promise<Event>
    updateEvent(
        id: string | number,
        eventData: Partial<Event>,
    ): Promise<Event | null>
    deleteEvent(id: string | number): Promise<boolean>
    getParticipants(): Promise<EventParticipant[]>
    getEventTypes(): Promise<EventType[]>
    importParticipants(
        eventId: string | number,
        participants: EventParticipant[],
    ): Promise<boolean>
    selfRegisterToEvent(
        eventId: string | number,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        participantData: any,
    ): Promise<boolean>
    registerParticipantAndMarkAttendance(
        eventId: string | number,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        participantData: any,
    ): Promise<boolean>
    recordAttendance(
        eventId: string | number,
        sessionId: string | number,
        participantId: string | number,
        attended: boolean,
        notes?: string,
    ): Promise<AttendanceRecord | null>
    getDashboardStats(): Promise<{
        totalProgramadas: number
        promedioAsistencia: number
        proximaReunion?: { title: string; date: string }
        eventosPorEstado: Array<{ status: EventStatus; count: number }>
    eventosPorDepartamento: Array<{ departamento: string; count: number }>
    }>
    getHierarchicalFilterData(): Promise<HierarchicalFilterData>
    getEventsForUser(userId: string | number): Promise<Event[]>
    justifyAbsence(
        eventId: string | number,
        userId: string | number,
        justification: string,
    ): Promise<boolean>
    updateEventStatus(
        eventId: string | number,
        status: EventStatus,
    ): Promise<boolean>

    // Métodos para manejo de estados de sesiones individuales
    updateSessionStatus(
        eventId: string | number,
        sessionId: string | number,
        status: AttendanceSessionStatus,
    ): Promise<boolean>
    getSessionStatus(
        eventId: string | number,
        sessionId: string | number,
    ): Promise<AttendanceSessionStatus | null>

    // Método para completar automáticamente sesiones en modo kiosco
    completeKioskSession(
        eventId: string | number,
        sessionId: string | number,
    ): Promise<boolean>

    // Método para validar la existencia de un participante antes del registro
    validateParticipantExists(
        eventId: string | number,
        participantData: ParticipantValidationData,
    ): Promise<ParticipantValidationResult>
}
