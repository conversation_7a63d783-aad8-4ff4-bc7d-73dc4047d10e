import dayjs from 'dayjs'

export const formatDate = (date: string): string => {
    return dayjs(date).format('DD MMM YYYY')
}

export const formatDateRange = (startDate: string, endDate?: string): string => {
    if (!endDate || startDate === endDate) {
        return formatDate(startDate)
    }

    const start = dayjs(startDate)
    const end = dayjs(endDate)

    if (start.year() !== end.year()) {
        return `${start.format('DD MMM YYYY')} - ${end.format('DD MMM YYYY')}`
    }

    if (start.month() !== end.month()) {
        return `${start.format('DD MMM')} - ${end.format('DD MMM YYYY')}`
    }

    return `${start.format('DD')} - ${end.format('DD MMM YYYY')}`
}

export const formatTime = (timeString: string | undefined | null): string => {
    if (!timeString) return 'No especificado'

    const parts = timeString.split(':')
    if (parts.length < 2) return 'Formato inválido'

    const [hours, minutes] = parts
    const hour = parseInt(hours, 10)
    if (isNaN(hour)) return 'Formato inválido'

    const ampm = hour >= 12 ? 'PM' : 'AM'
    const hour12 = hour % 12 || 12

    return `${hour12.toString().padStart(2, '0')}:${minutes} ${ampm}`
}

export default {
    formatDate,
    formatDateRange,
    formatTime
}

