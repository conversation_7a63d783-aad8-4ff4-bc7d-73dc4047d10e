# Este documento ha sido consolidado en `EVENT_WORKFLOWS.md` y se mantiene como referencia historica.

# Sistema de Estados para Sesiones de Asistencia - Documentación de Implementación

## Resumen
Se ha implementado un sistema completo de estados para las sesiones de asistencia de eventos, permitiendo que cada sesión tenga su propio estado independiente y que el evento completo solo se marque como completado cuando todas sus sesiones estén completadas.

## Estados de Sesiones Implementados

### Tipos de Estados
- **`pendiente`**: Sesión que aún no ha comenzado
- **`en_progreso`**: Sesión actualmente en desarrollo
- **`completada`**: Sesión finalizada con todos los registros completos
- **`cancelada`**: Sesión cancelada

### Transiciones de Estados Válidas
- `pendiente` → `en_progreso`, `cancelada`
- `en_progreso` → `completada`, `cancelada`
- `completada` → `en_progreso` (reapertura)
- `cancelada` → `pendiente`, `en_progreso` (reactivación)

## Componentes Actualizados

### 1. SessionStatusBadge
- **Ubicación**: `src/features/events/components/SessionStatusBadge/`
- **Funcionalidad**: Componente reutilizable para mostrar estados de sesiones
- **Características**:
  - Colores consistentes para cada estado
  - Iconos visuales opcionales
  - Tooltips informativos
  - Tamaños configurables (sm, md, lg)

### 2. SessionTable (Edición de Eventos)
- **Ubicación**: `src/features/events/components/SessionTable/`
- **Funcionalidad**: Tabla para editar sesiones en la creación/edición de eventos
- **Nuevas características**:
  - Columna de estado con badge visual
  - Selector para cambiar estado de sesiones
  - Validaciones de transiciones de estado

### 3. EventDetailView
- **Ubicación**: `src/features/events/views/EventDetail/`
- **Funcionalidad**: Vista de detalles del evento
- **Nuevas características**:
  - Columna de estado en tabla de sesiones
  - Información de progreso del evento
  - Indicadores visuales de estado

### 4. SelectSessionModal
- **Ubicación**: `src/features/events/components/SelectSessionModal/`
- **Funcionalidad**: Modal para seleccionar sesión de asistencia
- **Nuevas características**:
  - Columna de estado con badges
  - Botones habilitados/deshabilitados según estado
  - Tooltips informativos sobre disponibilidad

### 5. AttendanceView y FastAttendanceView
- **Ubicación**: `src/features/events/views/Attendance/` y `src/features/events/views/FastAttendance/`
- **Funcionalidad**: Vistas de registro de asistencia
- **Nuevas características**:
  - Actualización automática de estado de sesión
  - Lógica de completado automático para modo kiosco
  - Mensajes informativos sobre acciones automáticas
  - Validación de estado del evento completo

## Servicios Actualizados

### 1. IEventsService (Interfaz)
- **Nuevos métodos**:
  - `updateSessionStatus(eventId, sessionId, status)`
  - `getSessionStatus(eventId, sessionId)`
  - `completeKioskSession(eventId, sessionId)`

### 2. EventsMockService
- **Funcionalidad implementada**:
  - Actualización de estados de sesiones individuales
  - Completado automático de sesiones kiosco
  - Marcado automático de ausencias en modo kiosco

### 3. EventsApiService
- **Funcionalidad implementada**:
  - Endpoints para manejo de estados de sesiones
  - Integración con API real (pendiente de implementación backend)

## Utilidades Implementadas

### 1. eventStatusUtils.ts
- **Funciones principales**:
  - `canCompleteEvent()`: Valida si evento puede completarse
  - `determineEventStatus()`: Determina estado del evento basado en sesiones
  - `calculateEventProgress()`: Calcula porcentaje de progreso
  - `canRecordAttendance()`: Valida si se puede registrar asistencia
  - `getSessionStatusMessage()`: Mensajes descriptivos de estados

### 2. eventValidations.ts
- **Funciones principales**:
  - `validateEventCompletion()`: Validación de completado de evento
  - `validateSessionStatusChange()`: Validación de cambios de estado
  - `validateAttendanceRegistration()`: Validación de registro de asistencia
  - `validateKioskSessionCompletion()`: Validación de completado automático

### 3. ValidationMessage Component
- **Funcionalidad**: Componente para mostrar mensajes de validación consistentes
- **Tipos soportados**: error, warning, info

## Lógica de Negocio Implementada

### 1. Completado Automático de Eventos
- Un evento solo se marca como completado cuando TODAS sus sesiones están completadas
- El estado del evento se determina automáticamente basado en el estado de sus sesiones

### 2. Modo Kiosco Rápido
- Al completar una sesión en modo kiosco, se marcan automáticamente como "no asistió" todos los participantes sin registro
- Se muestra mensaje informativo al usuario sobre esta acción
- Se actualiza el estado de la sesión a "completada"

### 3. Transiciones de Estado Inteligentes
- Al registrar la primera asistencia en una sesión "pendiente", se cambia automáticamente a "en_progreso"
- Al finalizar registro en una sesión, se cambia a "completada"
- Se validan las transiciones de estado para prevenir cambios inválidos

## Datos Mock Actualizados

### Estados Realistas
- Eventos completados: sesiones con estado "completada"
- Eventos en progreso: sesiones con estado "en_progreso"
- Eventos programados: sesiones con estado "pendiente"
- Ejemplos de eventos multi-sesión con diferentes estados

## Endpoints de API Agregados

### Nuevos Endpoints
- `PATCH /events/{eventId}/sessions/{sessionId}/status`: Actualizar estado de sesión
- `GET /events/{eventId}/sessions/{sessionId}/status`: Obtener estado de sesión
- `POST /events/{eventId}/sessions/{sessionId}/complete-kiosk`: Completar sesión kiosco

## Flujos de Trabajo Implementados

### 1. Registro de Asistencia Individual
1. Usuario selecciona evento y sesión
2. Sistema valida que se puede registrar asistencia
3. Al registrar primera asistencia, sesión pasa a "en_progreso"
4. Usuario puede guardar progreso (mantiene "en_progreso") o finalizar (cambia a "completada")
5. Sistema actualiza estado del evento basado en todas las sesiones

### 2. Completado Automático en Modo Kiosco
1. Usuario finaliza registro en sesión kiosco
2. Sistema marca automáticamente como ausentes a participantes sin registro
3. Sesión se marca como "completada"
4. Sistema evalúa si evento completo debe marcarse como "completado"
5. Se muestra mensaje informativo sobre acciones automáticas

### 3. Validaciones de Estado
1. Sistema previene marcar evento como completado si hay sesiones pendientes
2. Se validan transiciones de estado de sesiones
3. Se muestran mensajes informativos sobre restricciones
4. Se proporcionan alternativas cuando una acción no es posible

## Pruebas Recomendadas

### 1. Flujo Básico
- [ ] Crear evento con múltiples sesiones
- [ ] Verificar estados iniciales ("pendiente")
- [ ] Registrar asistencia en una sesión
- [ ] Verificar cambio automático a "en_progreso"
- [ ] Completar sesión y verificar cambio a "completada"
- [ ] Verificar que evento no se marca como completado hasta que todas las sesiones estén completadas

### 2. Modo Kiosco
- [ ] Crear sesión en modo kiosco
- [ ] Registrar algunos participantes
- [ ] Finalizar sesión y verificar marcado automático de ausencias
- [ ] Verificar mensaje informativo mostrado

### 3. Validaciones
- [ ] Intentar marcar evento como completado con sesiones pendientes
- [ ] Verificar mensaje de error apropiado
- [ ] Intentar registrar asistencia en sesión completada
- [ ] Verificar restricción y mensaje informativo

### 4. Interfaz de Usuario
- [ ] Verificar badges de estado en todas las vistas
- [ ] Verificar información de progreso en detalles del evento
- [ ] Verificar habilitación/deshabilitación de botones según estado
- [ ] Verificar tooltips y mensajes informativos

## Consideraciones de Mantenimiento

### 1. Extensibilidad
- El sistema está diseñado para agregar fácilmente nuevos estados si es necesario
- Las validaciones están centralizadas y son fáciles de modificar
- Los componentes son reutilizables y consistentes

### 2. Consistencia
- Todos los estados se manejan de manera uniforme en toda la aplicación
- Los colores y estilos son consistentes
- Los mensajes siguen un patrón estándar

### 3. Performance
- Las validaciones son eficientes y no impactan el rendimiento
- Los componentes están optimizados para re-renderizado mínimo
- Los servicios mantienen consistencia entre mock y API real
