import { describe, it, expect, vi } from 'vitest'
import React from 'react'
import { exportEventParticipants } from '../exportEventParticipants'

var mocks: any

vi.mock('xlsx', () => {
    const utils = {
        book_new: vi.fn(() => ({})),
        json_to_sheet: vi.fn(() => ({})),
        book_append_sheet: vi.fn()
    }
    const writeFile = vi.fn()
    mocks = { utils, writeFile }
    return { utils, writeFile, default: { utils, writeFile } }
})

vi.mock('@/shared/components/ui/toast', () => ({
    default: { push: vi.fn() }
}))

vi.mock('@/shared/components/ui/Notification', () => ({
    default: ({ children }: any) => <div>{children}</div>
}))

const simpleEvent = {
    id: 1,
    title: 'Event',
    date: '2023-01-01',
    startTime: '10:00',
    status: 'programada',
    location: 'X',
    participantsInvited: [
        { id: 'p1', firstName: 'A', lastName: 'B', email: 'a@b.c' }
    ],
    sessions: []
} as any

describe('exportEventParticipants', () => {
    it('genera archivo excel', () => {
        exportEventParticipants(simpleEvent)
        expect(mocks.utils.book_new).toHaveBeenCalled()
        expect(mocks.utils.book_append_sheet).toHaveBeenCalledTimes(2)
        expect(mocks.writeFile).toHaveBeenCalledWith(expect.anything(), `Evento_${simpleEvent.id}.xlsx`)
    })
})
