/**
 * Hook personalizado para manejar el formulario de eventos.
 * @param options - Opciones que incluyen el id del evento en modo edición.
 * @returns Estado y manejadores para `EventFormView`.
 */
// Hook to manage state and handlers for EventFormView
import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import type { Event, EventParticipant, EventType, EventStatus } from '../types'
import EventsService from '../services/EventsService'
import toast from '@/shared/components/ui/toast'
import Notification from '@/shared/components/ui/Notification'
import * as XLSX from 'xlsx'

/** Opciones para inicializar el hook */
export interface UseEventFormOptions {
    id?: string
}

export const useEventForm = ({ id }: UseEventFormOptions) => {
    const navigate = useNavigate()
    const isEditMode = Boolean(id)
    const [eventData, setEventData] = useState<Event | null>(null)
    const [selectedParticipants, setSelectedParticipants] = useState<EventParticipant[]>([])
    const [eventTypes, setEventTypes] = useState<EventType[]>([])
    const [loading, setLoading] = useState(isEditMode)
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isImportModalOpen, setImportModalOpen] = useState(false)
    const formikRef = useRef<any>(null)

    useEffect(() => {
        if (isEditMode && id) {
            loadEventForEdit(id)
        }
        const loadTypes = async () => {
            try {
                const types = await EventsService.getEventTypes()
                setEventTypes(types)
            } catch (err) {
                console.error('Error al cargar tipos de evento:', err)
            }
        }
        loadTypes()
    }, [id])
    /** Carga los datos del evento para edición */

    const loadEventForEdit = async (eventId: string) => {
        setLoading(true)
        try {
            const event = await EventsService.getEventById(eventId)
            if (event) {
                setEventData(event)
                setSelectedParticipants(event.participantsInvited || [])
            }
        } catch (err) {
            console.error('Error al cargar el evento:', err)
        } finally {
            setLoading(false)
        }
    }
    /** Envía el formulario creando o actualizando el evento */

    const handleSubmit = async (values: any) => {
        try {
            let dateString = values.date
            if (values.date instanceof Date) {
                dateString = values.date.toISOString().split('T')[0]
            }
            const { typeId, ...rest } = values
            const selectedType = eventTypes.find((t) => t.id.toString() === typeId?.toString())
            const eventToSave: Partial<Event> = {
                ...rest,
                eventType: selectedType,
                date: dateString,
                participantsInvited: selectedParticipants,
                invitingDepartment: values.invitingDepartment,
                sessions: values.sessions,
            }
            if (isEditMode && id) {
                await EventsService.updateEvent(id, eventToSave)
                toast.push(
                    <Notification title="Evento actualizado" type="success">
                    El evento ha sido actualizado correctamente.
                </Notification>
                )
            } else {
                await EventsService.createEvent(eventToSave as Omit<Event, 'id'>)
                toast.push(
                    <Notification title="Evento creado" type="success">
                    El evento ha sido creado correctamente.
                </Notification>
                )
            }
            navigate('/events/list')
        } catch (error) {
            console.error('Error al guardar el evento:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    Ocurrió un error al guardar el evento. Por favor, inténtelo de nuevo.
                </Notification>
            )
        }
    }

    const handleOpenModal = () => setIsModalOpen(true)
    const handleCloseModal = () => setIsModalOpen(false)
    const handleOpenImportModal = () => setImportModalOpen(true)
    const handleCloseImportModal = () => setImportModalOpen(false)

    /** Descarga una plantilla para importar participantes */
    const handleDownloadTemplate = () => {
        const templateData = [
            { Nombre: 'Nombre', Apellido: 'Apellido', Email: '<EMAIL>' }
        ]
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(templateData)
        XLSX.utils.book_append_sheet(wb, ws, 'Plantilla')
        XLSX.writeFile(wb, 'plantilla.xlsx')
    }

    /** Procesa el archivo Excel de participantes importados */
    const handleFileChange = async (files: File[]) => {
        const file = files[0]
        const reader = new FileReader()
        reader.onload = async (evt) => {
            try {
                const bstr = evt.target?.result
                const wb = XLSX.read(bstr as string, { type: 'binary' })
                const wsname = wb.SheetNames[0]
                const ws = wb.Sheets[wsname]
                const data = XLSX.utils.sheet_to_json(ws)
                if (data.length === 0) {
                    toast.push(
                        <Notification title="Archivo vacío" type="warning">
                            El archivo Excel no contiene datos para importar.
                        </Notification>
                    )
                    return
                }
                // For simplicity, just add all participants
                setSelectedParticipants((prev) => [
                    ...prev,
                    ...(data as any[]).map((p, i) => ({
                        id: Date.now() + i,
                        firstName: p['Nombre'] || '',
                        lastName: p['Apellido'] || '',
                        email: p['Email'] || '',
                    })) as any,
                ])
                toast.push(
                    <Notification title="Importación" type="success">
                        Participantes importados
                    </Notification>
                )
                setImportModalOpen(false)
            } catch (e) {
                console.error('Error al parsear el archivo Excel', e)
                toast.push(
                    <Notification title="Error de importación" type="danger">
                        No se pudo parsear el archivo. Verifique que sea un archivo Excel válido.
                    </Notification>
                )
            }
        }
        reader.readAsBinaryString(file)
    }
    /** Objeto expuesto para usar en la vista */

    return {
        isEditMode,
        loading,
        eventData,
        selectedParticipants,
        eventTypes,
        isModalOpen,
        isImportModalOpen,
        formikRef,
        handleSubmit,
        handleOpenModal,
        handleCloseModal,
        handleDownloadTemplate,
        handleFileChange,
        handleOpenImportModal,
        handleCloseImportModal,
        setSelectedParticipants,
    }
}

export default useEventForm
