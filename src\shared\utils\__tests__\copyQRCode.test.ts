import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import copyQRCode from '../copyQRCode'

// Mock del toast
vi.mock('@/shared/components/ui/toast', () => ({
    default: {
        push: vi.fn()
    }
}))

// Mock de las APIs del navegador
const mockClipboard = {
    write: vi.fn()
}

const mockURL = {
    createObjectURL: vi.fn(() => 'mock-url'),
    revokeObjectURL: vi.fn()
}

const mockCanvas = {
    width: 0,
    height: 0,
    getContext: vi.fn(() => ({
        drawImage: vi.fn(),
        fillStyle: '',
        fillRect: vi.fn()
    })),
    toBlob: vi.fn()
}

const mockImage = {
    onload: null as any,
    onerror: null as any,
    src: '',
    width: 256,
    height: 256
}

// Mock de XMLSerializer
const mockXMLSerializer = {
    serializeToString: vi.fn(() => '<svg></svg>')
}

describe('copyQRCode', () => {
    beforeEach(() => {
        // Configurar mocks globales
        Object.defineProperty(global, 'navigator', {
            value: {
                clipboard: mockClipboard
            },
            writable: true
        })

        Object.defineProperty(global, 'URL', {
            value: mockURL,
            writable: true
        })

        Object.defineProperty(global, 'XMLSerializer', {
            value: vi.fn(() => mockXMLSerializer),
            writable: true
        })

        Object.defineProperty(global, 'Image', {
            value: vi.fn(() => mockImage),
            writable: true
        })

        Object.defineProperty(global, 'Blob', {
            value: vi.fn(),
            writable: true
        })

        Object.defineProperty(global, 'ClipboardItem', {
            value: vi.fn(),
            writable: true
        })

        // Mock de document.createElement para canvas
        global.document.createElement = vi.fn((tagName) => {
            if (tagName === 'canvas') {
                return mockCanvas as any
            }
            return {} as any
        })

        // Limpiar mocks
        vi.clearAllMocks()
    })

    afterEach(() => {
        vi.restoreAllMocks()
    })

    describe('Validaciones de entrada', () => {
        it('debería retornar false si no se proporciona elemento QR', async () => {
            const result = await copyQRCode(null)
            expect(result).toBe(false)
        })

        it('debería retornar false si el navegador no soporta clipboard API', async () => {
            // Simular navegador sin soporte
            Object.defineProperty(global, 'navigator', {
                value: {
                    clipboard: null
                },
                writable: true
            })

            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            const result = await copyQRCode(mockSVG)
            expect(result).toBe(false)
        })

        it('debería retornar false si clipboard.write no está disponible', async () => {
            // Simular navegador con clipboard pero sin write
            Object.defineProperty(global, 'navigator', {
                value: {
                    clipboard: {}
                },
                writable: true
            })

            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            const result = await copyQRCode(mockSVG)
            expect(result).toBe(false)
        })
    })

    describe('Proceso de copia exitoso', () => {
        it('debería copiar el QR code exitosamente', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            
            // Configurar mocks para éxito
            mockCanvas.toBlob.mockImplementation((callback) => {
                callback(new Blob())
            })
            mockClipboard.write.mockResolvedValue(undefined)

            // Simular carga exitosa de imagen
            const promise = copyQRCode(mockSVG)
            
            // Simular que la imagen se carga
            if (mockImage.onload) {
                mockImage.onload()
            }

            const result = await promise
            expect(result).toBe(true)
        })

        it('debería usar mensajes personalizados', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            const customSuccess = 'QR copiado con éxito'
            const customError = 'Error personalizado'
            
            // Configurar mocks para éxito
            mockCanvas.toBlob.mockImplementation((callback) => {
                callback(new Blob())
            })
            mockClipboard.write.mockResolvedValue(undefined)

            const promise = copyQRCode(mockSVG, customSuccess, customError)
            
            // Simular que la imagen se carga
            if (mockImage.onload) {
                mockImage.onload()
            }

            const result = await promise
            expect(result).toBe(true)
        })
    })

    describe('Manejo de errores', () => {
        it('debería manejar error al crear canvas context', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            
            // Simular error en getContext
            mockCanvas.getContext.mockReturnValue(null)

            const promise = copyQRCode(mockSVG)
            
            // Simular que la imagen se carga
            if (mockImage.onload) {
                mockImage.onload()
            }

            const result = await promise
            expect(result).toBe(false)
        })

        it('debería manejar error al convertir canvas a blob', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            
            // Simular error en toBlob
            mockCanvas.toBlob.mockImplementation((callback) => {
                callback(null)
            })

            const promise = copyQRCode(mockSVG)
            
            // Simular que la imagen se carga
            if (mockImage.onload) {
                mockImage.onload()
            }

            const result = await promise
            expect(result).toBe(false)
        })

        it('debería manejar error al copiar al portapapeles', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            
            // Configurar mocks para error en clipboard
            mockCanvas.toBlob.mockImplementation((callback) => {
                callback(new Blob())
            })
            mockClipboard.write.mockRejectedValue(new Error('Clipboard error'))

            const promise = copyQRCode(mockSVG)
            
            // Simular que la imagen se carga
            if (mockImage.onload) {
                mockImage.onload()
            }

            const result = await promise
            expect(result).toBe(false)
        })

        it('debería manejar error al cargar imagen', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')

            const promise = copyQRCode(mockSVG)
            
            // Simular error al cargar imagen
            if (mockImage.onerror) {
                mockImage.onerror()
            }

            const result = await promise
            expect(result).toBe(false)
        })
    })

    describe('Limpieza de recursos', () => {
        it('debería limpiar URL object después del éxito', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
            
            // Configurar mocks para éxito
            mockCanvas.toBlob.mockImplementation((callback) => {
                callback(new Blob())
            })
            mockClipboard.write.mockResolvedValue(undefined)

            const promise = copyQRCode(mockSVG)
            
            // Simular que la imagen se carga
            if (mockImage.onload) {
                mockImage.onload()
            }

            await promise
            expect(mockURL.revokeObjectURL).toHaveBeenCalled()
        })

        it('debería limpiar URL object después del error', async () => {
            const mockSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg')

            const promise = copyQRCode(mockSVG)
            
            // Simular error al cargar imagen
            if (mockImage.onerror) {
                mockImage.onerror()
            }

            await promise
            expect(mockURL.revokeObjectURL).toHaveBeenCalled()
        })
    })
})