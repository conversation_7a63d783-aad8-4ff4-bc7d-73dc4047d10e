# Flujo: Gestión de Participantes con Estructura Jerárquica

## Descripción General

Este flujo describe cómo se gestionan los participantes en el sistema, desde su obtención con estructura jerárquica hasta su creación y filtrado, utilizando la API de Strapi v5 con populate estratégico y manejo dual de datos.

## Componentes Involucrados

### Servicios
- **ParticipantsService**: Servicio principal para operaciones CRUD de participantes
- **ApiService**: Servicio base para comunicación con la API
- **MediaService**: Manejo de archivos multimedia (avatares)

### Tipos y Interfaces
- **Participant**: Interface principal con estructura jerárquica
- **CreateParticipantRequest/UpdateParticipantRequest**: Interfaces para requests
- **Field, Zone, District, Church**: Interfaces jerárquicas
- **ParticipantFilters**: Interface para filtros de búsqueda

### Componentes UI
- **EventFormView**: Formulario de eventos con gestión de participantes
- **ParticipantSelector**: Selector de participantes con filtros jerárquicos

### Funciones Utilitarias
- **enrichParticipantWithNames**: Enriquece participantes con IDs de compatibilidad
- **participantToCreateRequest**: Convierte participante a formato de creación
- **participantToUpdateRequest**: Convierte participante a formato de actualización

## Flujo Detallado

### 1. Obtención de Participantes (GET)

```mermaid
graph TD
    A[Solicitar Participantes] --> B[Construir Populate Estratégico]
    B --> C[Aplicar Filtros con documentId]
    C --> D[Ejecutar Request a Strapi]
    D --> E[Recibir Objetos Jerárquicos Completos]
    E --> F[Enriquecer con IDs de Compatibilidad]
    F --> G[Retornar Participantes Enriquecidos]
```

#### Pasos Detallados:

1. **Construcción de Populate**
   ```typescript
   // Populate específico para evitar sobrecarga de datos
   populate: {
     avatar: true,
     userAccount: { populate: ['avatar'] },
     fieldAssignment: { populate: ['*'] },
     zoneAssignment: { populate: ['field'] },
     districtAssignment: { populate: ['zone'] },
     churchAssignment: { populate: ['district'] }
   }
   ```

2. **Aplicación de Filtros**
   ```typescript
   // Filtros usando documentId para Strapi v5
   filters: {
     fieldAssignment: { documentId: 'field-1' },
     zoneAssignment: { documentId: 'zone-1' },
     // ... más filtros
   }
   ```

3. **Enriquecimiento de Datos**
   ```typescript
   // Agregar IDs de compatibilidad para filtros locales
   participants.map(enrichParticipantWithNames)
   ```

### 2. Creación de Participantes (POST)

```mermaid
graph TD
    A[Datos del Formulario] --> B[Validar Datos de Entrada]
    B --> C[Convertir a CreateParticipantRequest]
    C --> D[Envolver en Objeto 'data' para Strapi]
    D --> E[Ejecutar POST Request]
    E --> F[Recibir Participante Creado]
    F --> G[Retornar Participante con Estructura Completa]
```

#### Pasos Detallados:

1. **Preparación de Datos**
   ```typescript
   // Solo IDs para relaciones en POST
   const participantPayload = {
     firstName: 'Juan',
     lastName: 'Pérez',
     email: '<EMAIL>',
     fieldAssignment: 'field-doc-id', // Solo documentId
     zoneAssignment: 'zone-doc-id',
     // ... más relaciones
   }
   ```

2. **Formato Strapi v5**
   ```typescript
   // Envolver en objeto 'data'
   const strapiPayload = { data: participantPayload }
   ```

### 3. Filtrado Jerárquico

```mermaid
graph TD
    A[Seleccionar Filtro Jerárquico] --> B[Aplicar Filtro a Nivel API]
    B --> C[Aplicar Filtro Local Adicional]
    C --> D[Mostrar Resultados Filtrados]
    D --> E[Actualizar Filtros Dependientes]
```

#### Pasos Detallados:

1. **Filtro por Campo (Field)**
   - Filtra participantes por asociación/campo
   - Actualiza opciones disponibles de zonas

2. **Filtro por Zona**
   - Filtra participantes por zona específica
   - Actualiza opciones disponibles de distritos

3. **Filtro por Distrito**
   - Filtra participantes por distrito específico
   - Actualiza opciones disponibles de iglesias

4. **Filtro por Iglesia**
   - Filtra participantes por iglesia específica
   - Resultado final más específico

### 4. Búsqueda por Email

```mermaid
graph TD
    A[Ingresar Email] --> B[Construir Filtro de Email]
    B --> C[Ejecutar Búsqueda con Populate]
    C --> D{¿Participante Encontrado?}
    D -->|Sí| E[Enriquecer y Retornar Participante]
    D -->|No| F[Retornar null]
```

### 5. Importación desde Excel

```mermaid
graph TD
    A[Cargar Archivo Excel] --> B[Parsear Datos]
    B --> C[Mapear Datos Jerárquicos]
    C --> D[Validar Email Existente]
    D --> E{¿Participante Existe?}
    E -->|Sí| F[Usar Participante Existente]
    E -->|No| G[Crear Nuevo Participante]
    G --> H[Agregar a Lista de Participantes]
    F --> H
    H --> I[Continuar con Siguiente Fila]
```

## Manejo de Errores

### Errores de API
- **Error 400**: Datos de entrada inválidos
  - Validar estructura de datos antes del envío
  - Mostrar mensajes específicos al usuario

- **Error 404**: Participante no encontrado
  - Manejar búsquedas que no retornan resultados
  - Ofrecer opciones de creación

- **Error 500**: Error interno del servidor
  - Mostrar mensaje genérico al usuario
  - Registrar error detallado en consola

### Errores de Populate
- **Relaciones faltantes**: Manejar casos donde las relaciones no existen
- **Populate fallido**: Fallback a estructura básica sin relaciones

### Errores de Filtros
- **Filtros inválidos**: Validar filtros antes de aplicar
- **Combinaciones incompatibles**: Detectar y corregir filtros contradictorios

## Consideraciones de Seguridad

### Validación de Datos
- Validar todos los datos de entrada antes de enviar a la API
- Sanitizar datos para prevenir inyección de código

### Autorización
- Verificar permisos del usuario para operaciones CRUD
- Implementar filtros de seguridad a nivel de API

### Datos Sensibles
- No exponer información sensible en logs
- Manejar datos personales según regulaciones de privacidad

## Consideraciones de Rendimiento

### Populate Optimizado
- Usar populate específico en lugar de `populate: '*'`
- Cargar solo las relaciones necesarias para cada contexto

### Paginación
- Implementar paginación para listas grandes de participantes
- Usar parámetros de página y tamaño apropiados

### Cache Local
- Considerar cache local para datos jerárquicos que cambian poco
- Invalidar cache cuando sea necesario

## Diagramas de Arquitectura

### Estructura de Datos
```
Participant
├── Información Personal (firstName, lastName, email, phone)
├── Rol Eclesiástico (ecclesiasticalRole)
├── Avatar (Media)
├── Cuenta de Usuario (UserAccount) [Opcional]
└── Asignaciones Jerárquicas
    ├── Campo/Asociación (fieldAssignment)
    ├── Zona (zoneAssignment)
    ├── Distrito (districtAssignment)
    └── Iglesia (churchAssignment)
```

### Flujo de Datos
```
Frontend Component
    ↓ (Request con filtros)
ParticipantsService
    ↓ (Populate estratégico)
ApiService
    ↓ (HTTP Request)
Strapi v5 API
    ↓ (Objetos jerárquicos)
ApiService
    ↓ (Response data)
ParticipantsService
    ↓ (Enriquecimiento)
Frontend Component
```

## Validación del Flujo

### Criterios de Éxito
- ✅ Participantes se cargan con estructura jerárquica completa
- ✅ Filtros funcionan correctamente con documentId
- ✅ Creación de participantes usa solo IDs en requests
- ✅ Búsquedas retornan resultados enriquecidos
- ✅ Manejo de errores es robusto y informativo

### Métricas de Rendimiento
- Tiempo de carga de participantes: < 2 segundos
- Tiempo de filtrado: < 500ms
- Tiempo de creación: < 1 segundo
- Uso de memoria: Optimizado con populate específico
