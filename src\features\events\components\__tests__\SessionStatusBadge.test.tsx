import { render } from '@testing-library/react'
import SessionStatusBadge from '../SessionStatusBadge/SessionStatusBadge'
import React from 'react'

describe('SessionStatusBadge', () => {
  it('muestra el texto segun el estado', () => {
    const { getByText } = render(<SessionStatusBadge status="en_progreso" />)
    expect(getByText('En Progreso')).toBeInTheDocument()
  })

  it('muestra el icono cuando showText es false', () => {
    const { container } = render(
      <SessionStatusBadge status="en_progreso" showText={false} />
    )
    expect(container.textContent).toContain('🔄')
  })
})
