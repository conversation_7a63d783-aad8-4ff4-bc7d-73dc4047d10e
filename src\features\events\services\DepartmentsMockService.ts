import { mockDepartments } from '@/mock/data/departmentsData'
import type { Department, DepartmentStats } from '../types'
import type { IDepartmentsService } from './DepartmentsService.interface'
import type { PaginatedResponse } from '@/shared/types/api'

/**
 * Servicio mock que simula las operaciones de departamentos
 * para desarrollo y testing.
 */
const DepartmentsMockService: IDepartmentsService = {
    /**
     * Obtiene todos los departamentos (simulado) con paginación
     */
    async getDepartments(): Promise<PaginatedResponse<Department>> {
        // Simular delay de red
        await new Promise(resolve => setTimeout(resolve, 500))
        
        return {
            data: mockDepartments,
            meta: {
                pagination: {
                    page: 1,
                    pageSize: mockDepartments.length,
                    pageCount: 1,
                    total: mockDepartments.length
                }
            }
        }
    },

    /**
     * Obtiene solo los departamentos activos (simulado) con paginación
     */
    async getActiveDepartments(): Promise<PaginatedResponse<Department>> {
        // Simular delay de red
        await new Promise(resolve => setTimeout(resolve, 300))
        
        const activeDepartments = mockDepartments.filter(dept => dept.active)
        
        return {
            data: activeDepartments,
            meta: {
                pagination: {
                    page: 1,
                    pageSize: activeDepartments.length,
                    pageCount: 1,
                    total: activeDepartments.length
                }
            }
        }
    },

    /**
     * Obtiene un departamento por su identificador
     * @param id - ID del departamento
     */
    getDepartmentById(id) {
        const department = mockDepartments.find(dept => dept.id.toString() === id.toString())
        return Promise.resolve(department || null)
    },

    /**
     * Obtiene estadísticas de un departamento
     * @param id - ID del departamento
     */
    getDepartmentStats(id) {
        // Simular estadísticas para el departamento
        const stats: DepartmentStats = {
            totalEvents: Math.floor(Math.random() * 50) + 10,
            activeEvents: Math.floor(Math.random() * 10) + 1,
            completedEvents: Math.floor(Math.random() * 40) + 5,
            averageAttendance: Math.floor(Math.random() * 50) + 50,
        }
        return Promise.resolve(stats)
    },
}

export default DepartmentsMockService