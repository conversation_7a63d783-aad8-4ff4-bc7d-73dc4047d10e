import type { Event, AttendanceSessionStatus } from '../types'
import { canCompleteEvent, getPendingSessions, getInProgressSessions } from './eventStatusUtils'

/**
 * Utilidades para validaciones de eventos y sesiones de asistencia
 */

/**
 * Resultado de una validación
 */
export interface ValidationResult {
    isValid: boolean
    message?: string
    type?: 'error' | 'warning' | 'info'
}

/**
 * Valida si un evento puede marcarse como completado
 */
export function validateEventCompletion(event: Event): ValidationResult {
    if (!canCompleteEvent(event)) {
        const pendingSessions = getPendingSessions(event)
        const inProgressSessions = getInProgressSessions(event)
        
        let message = 'No se puede completar el evento. '
        
        if (pendingSessions.length > 0) {
            message += `Hay ${pendingSessions.length} sesión(es) pendiente(s). `
        }
        
        if (inProgressSessions.length > 0) {
            message += `Hay ${inProgressSessions.length} sesión(es) en progreso. `
        }
        
        message += 'Complete todas las sesiones antes de marcar el evento como completado.'
        
        return {
            isValid: false,
            message,
            type: 'error'
        }
    }
    
    return { isValid: true }
}

/**
 * Valida si se puede cambiar el estado de una sesión
 */
export function validateSessionStatusChange(
    currentStatus: AttendanceSessionStatus,
    newStatus: AttendanceSessionStatus,
    hasAttendanceRecords: boolean = false
): ValidationResult {
    // No permitir cambiar de completada a pendiente si hay registros de asistencia
    if (currentStatus === 'completada' && newStatus === 'pendiente' && hasAttendanceRecords) {
        return {
            isValid: false,
            message: 'No se puede cambiar una sesión completada con registros de asistencia a pendiente. Considere cambiarla a "en progreso" si necesita hacer modificaciones.',
            type: 'error'
        }
    }
    
    // Advertir al cambiar de completada a en progreso
    if (currentStatus === 'completada' && newStatus === 'en_progreso') {
        return {
            isValid: true,
            message: 'La sesión será reabierta para permitir modificaciones en los registros de asistencia.',
            type: 'warning'
        }
    }
    
    // Advertir al cancelar una sesión con registros
    if (newStatus === 'cancelada' && hasAttendanceRecords) {
        return {
            isValid: true,
            message: 'Al cancelar la sesión, los registros de asistencia existentes se mantendrán pero la sesión no contará para el progreso del evento.',
            type: 'warning'
        }
    }
    
    return { isValid: true }
}

/**
 * Valida si se puede registrar asistencia en una sesión
 */
export function validateAttendanceRegistration(sessionStatus: AttendanceSessionStatus): ValidationResult {
    if (sessionStatus === 'completada') {
        return {
            isValid: false,
            message: 'No se puede registrar asistencia en una sesión completada. Cambie el estado de la sesión a "en progreso" para permitir modificaciones.',
            type: 'error'
        }
    }
    
    if (sessionStatus === 'cancelada') {
        return {
            isValid: false,
            message: 'No se puede registrar asistencia en una sesión cancelada.',
            type: 'error'
        }
    }
    
    if (sessionStatus === 'pendiente') {
        return {
            isValid: true,
            message: 'La sesión será marcada automáticamente como "en progreso" al registrar la primera asistencia.',
            type: 'info'
        }
    }
    
    return { isValid: true }
}

/**
 * Valida si se puede completar una sesión en modo kiosco
 */
export function validateKioskSessionCompletion(
    sessionStatus: AttendanceSessionStatus,
    attendanceMode: 'normal' | 'kiosk',
    totalParticipants: number,
    recordedParticipants: number
): ValidationResult {
    if (attendanceMode !== 'kiosk') {
        return {
            isValid: false,
            message: 'Esta función solo está disponible para sesiones en modo kiosco.',
            type: 'error'
        }
    }
    
    if (sessionStatus === 'completada') {
        return {
            isValid: false,
            message: 'La sesión ya está completada.',
            type: 'error'
        }
    }
    
    if (sessionStatus === 'cancelada') {
        return {
            isValid: false,
            message: 'No se puede completar una sesión cancelada.',
            type: 'error'
        }
    }
    
    const unrecordedParticipants = totalParticipants - recordedParticipants
    
    if (unrecordedParticipants > 0) {
        return {
            isValid: true,
            message: `Al completar la sesión, ${unrecordedParticipants} participante(s) sin registro serán marcados automáticamente como ausentes.`,
            type: 'warning'
        }
    }
    
    return {
        isValid: true,
        message: 'Todos los participantes tienen registro de asistencia.',
        type: 'info'
    }
}

/**
 * Obtiene un mensaje informativo sobre el progreso de un evento
 */
export function getEventProgressInfo(event: Event): ValidationResult {
    const totalSessions = event.sessions?.length || 0
    
    if (totalSessions === 0) {
        return {
            isValid: true,
            message: 'Este evento no tiene sesiones de asistencia configuradas.',
            type: 'info'
        }
    }
    
    const pendingSessions = getPendingSessions(event)
    const inProgressSessions = getInProgressSessions(event)
    const completedSessions = event.sessions?.filter(s => s.status === 'completada') || []
    const cancelledSessions = event.sessions?.filter(s => s.status === 'cancelada') || []
    
    let message = `Progreso del evento: ${completedSessions.length}/${totalSessions} sesiones completadas`
    
    if (pendingSessions.length > 0) {
        message += `, ${pendingSessions.length} pendiente(s)`
    }
    
    if (inProgressSessions.length > 0) {
        message += `, ${inProgressSessions.length} en progreso`
    }
    
    if (cancelledSessions.length > 0) {
        message += `, ${cancelledSessions.length} cancelada(s)`
    }
    
    message += '.'
    
    const type = completedSessions.length === totalSessions ? 'info' : 'warning'
    
    return {
        isValid: true,
        message,
        type
    }
}

/**
 * Valida si se puede eliminar una sesión
 */
export function validateSessionDeletion(
    sessionStatus: AttendanceSessionStatus,
    hasAttendanceRecords: boolean,
    isDefaultSession: boolean
): ValidationResult {
    if (isDefaultSession) {
        return {
            isValid: false,
            message: 'No se puede eliminar la sesión principal del evento.',
            type: 'error'
        }
    }
    
    if (sessionStatus === 'completada' && hasAttendanceRecords) {
        return {
            isValid: false,
            message: 'No se puede eliminar una sesión completada con registros de asistencia. Considere cancelarla en su lugar.',
            type: 'error'
        }
    }
    
    if (hasAttendanceRecords) {
        return {
            isValid: true,
            message: 'La sesión tiene registros de asistencia. ¿Está seguro de que desea eliminarla?',
            type: 'warning'
        }
    }
    
    return { isValid: true }
}
