/**
 * Hook para manejar el estado de asistencia de eventos.
 * @param eventId - Identificador del evento.
 * @param sessionId - Identificador de la sesión.
 * @returns Estado y funciones para las vistas de asistencia.
 */
import { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import type { Event } from '../types'
import EventsService from '../services/EventsService'
import toast from '@/shared/components/ui/toast'
import { sanitizeEvent } from '../utils/eventDataUtils'
import { determineEventStatus } from '../utils/eventStatusUtils'
import * as Yup from 'yup'

/** Estado de asistencia de un participante dentro de la sesión */
export interface ParticipantAttendanceState {
    status?: 'asistio' | 'no_asistio'
    reason?: string
    isPartial?: boolean
    partialComment?: string
    timeStayed?: string
}

/** Tipos de error para manejo centralizado */
type ErrorType = 'AUTH' | 'NOT_FOUND' | 'SERVER' | 'NETWORK' | 'VALIDATION' | 'UNKNOWN'

/** Configuración de errores */
const ERROR_CONFIG = {
    AUTH: { message: 'Su sesión ha expirado. Por favor, inicie sesión nuevamente.', redirect: '/auth/login' },
    NOT_FOUND: { message: 'El evento solicitado no fue encontrado.' },
    SERVER: { message: 'Error interno del servidor. Por favor, intente más tarde.' },
    NETWORK: { message: 'Error de conexión. Verifique su conexión a internet.' },
    VALIDATION: { message: 'Error de validación en los datos proporcionados.' },
    UNKNOWN: { message: 'Ocurrió un error inesperado.' }
} as const

export const useAttendance = (eventId?: string, sessionId?: string) => {
    const navigate = useNavigate()
    const { navigateBack, getBackButtonText } = useNavigationContext()
    const [event, setEvent] = useState<Event | null>(null)
    const [participantStates, setParticipantStates] = useState<Record<string, ParticipantAttendanceState>>({})
    const [loading, setLoading] = useState(true)
    const [saving, setSaving] = useState(false)
    const [error, setError] = useState<string | null>(null)

    /** Manejo centralizado de errores */
    const handleError = useCallback((error: any, context: string): ErrorType => {
        console.error(`Error en ${context}:`, error)
        
        let errorType: ErrorType = 'UNKNOWN'
        let errorMessage = ERROR_CONFIG.UNKNOWN.message
        
        if (error?.response?.status === 401 || error?.response?.status === 403) {
            errorType = 'AUTH'
            errorMessage = ERROR_CONFIG.AUTH.message
            setTimeout(() => navigate(ERROR_CONFIG.AUTH.redirect), 2000)
        } else if (error?.response?.status === 404) {
            errorType = 'NOT_FOUND'
            errorMessage = ERROR_CONFIG.NOT_FOUND.message
        } else if (error?.response?.status === 500) {
            errorType = 'SERVER'
            errorMessage = ERROR_CONFIG.SERVER.message
        } else if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
            errorType = 'NETWORK'
            errorMessage = ERROR_CONFIG.NETWORK.message
        } else if (error?.response?.data?.message) {
            errorMessage = error.response.data.message
        }
        
        setError(errorMessage)
        return errorType
    }, [navigate])

    /** Inicializa el estado de un participante basado en registros existentes */
    const initializeParticipantState = useCallback((participant: any, attendanceRecord?: any): ParticipantAttendanceState => {
        if (attendanceRecord) {
            return {
                status: attendanceRecord.attended ? 'asistio' : 'no_asistio',
                reason: attendanceRecord.notes || '',
                isPartial: attendanceRecord.partialAttendance || false,
                partialComment: attendanceRecord.partialAttendanceComment || '',
                timeStayed: attendanceRecord.timeStayed || '',
            }
        }
        return {
            status: undefined,
            reason: '',
            isPartial: false,
            partialComment: '',
            timeStayed: '',
        }
    }, [])

    /** Carga el evento y prepara el estado inicial de los participantes */
    const loadEvent = useCallback(async () => {
        if (!eventId || !sessionId) return
        
        setLoading(true)
        setError(null)
        
        try {
            const rawEvent = await EventsService.getEventById(eventId)
            const eventData = rawEvent ? sanitizeEvent(rawEvent) : null
            const currentSession = eventData?.sessions.find(s => s.id.toString() === sessionId)
            
            if (!eventData || !currentSession) {
                setError('No se encontró el evento solicitado')
                return
            }

            setEvent({
                ...eventData,
                attendanceRecords: currentSession.attendanceRecords || [],
                eventStatus: determineEventStatus(eventData),
            })

            // Inicializar estados de participantes
            const initialStates: Record<string, ParticipantAttendanceState> = {}
            eventData.participantsInvited?.forEach((participant) => {
                const participantId = participant.id.toString()
                const attendanceRecord = currentSession.attendanceRecords?.find(
                    record => record.person.id.toString() === participantId
                )
                initialStates[participantId] = initializeParticipantState(participant, attendanceRecord)
            })
            
            setParticipantStates(initialStates)
        } catch (error) {
            handleError(error, 'cargar el evento')
        } finally {
            setLoading(false)
        }
    }, [eventId, sessionId, handleError, initializeParticipantState])

    useEffect(() => {
        loadEvent()
    }, [loadEvent])

    /** Actualiza el estado de un participante */
    const updateParticipantState = useCallback((participantId: string, updates: Partial<ParticipantAttendanceState>) => {
        setParticipantStates(prev => ({
            ...prev,
            [participantId]: {
                ...prev[participantId],
                ...updates,
            },
        }))
    }, [])

    /** Factory para crear handlers de cambio de estado */
    const createStateHandler = useCallback((field: keyof ParticipantAttendanceState) => {
        return (participantId: string, value: any) => {
            updateParticipantState(participantId, { [field]: value })
        }
    }, [updateParticipantState])

    /** Handlers específicos para cada campo */
    const handleAttendanceStatusChange = useMemo(() => createStateHandler('status'), [createStateHandler])
    const handleReasonChange = useMemo(() => createStateHandler('reason'), [createStateHandler])
    const handlePartialAttendanceChange = useMemo(() => createStateHandler('isPartial'), [createStateHandler])
    const handlePartialCommentChange = useMemo(() => createStateHandler('partialComment'), [createStateHandler])
    const handleTimeStayedChange = useMemo(() => createStateHandler('timeStayed'), [createStateHandler])

    /** Esquemas de validación memoizados */
    const validationSchemas = useMemo(() => {
        const baseValidation = {
            status: Yup.string().oneOf(['asistio', 'no_asistio']).required('El estado de asistencia es requerido'),
            isPartial: Yup.boolean(),
            timeStayed: Yup.string().when(['status', 'isPartial'], {
                is: (status: string, isPartial: boolean) => status === 'asistio' && isPartial,
                then: (schema) => schema.required('El tiempo de permanencia es requerido para asistencia parcial'),
                otherwise: (schema) => schema.notRequired(),
            }),
        }

        return {
            progress: Yup.object().shape({
                ...baseValidation,
                reason: Yup.string().when('status', {
                    is: 'no_asistio',
                    then: (schema) => schema.required('La razón es requerida cuando no asiste'),
                    otherwise: (schema) => schema.notRequired(),
                }),
                partialComment: Yup.string().when(['status', 'isPartial'], {
                    is: (status: string, isPartial: boolean) => status === 'asistio' && isPartial,
                    then: (schema) => schema.required('El comentario es requerido para asistencia parcial'),
                    otherwise: (schema) => schema.notRequired(),
                }),
            }),
            finalize: Yup.object().shape(baseValidation)
        }
    }, [])

    /** Construye los registros de asistencia a partir del estado actual */
    const buildRecords = useCallback(() => {
        if (!event?.participantsInvited) return []
        
        return event.participantsInvited
            .filter(participant => {
                const participantId = participant.id.toString()
                return participantStates[participantId]?.status
            })
            .map(participant => {
                const participantId = participant.id.toString()
                const state = participantStates[participantId]
                const existingRecord = event.attendanceRecords?.find(
                    record => record.person.id.toString() === participantId
                )
                
                return {
                    id: existingRecord?.id || `${Date.now()}-${participantId}`,
                    person: {
                        id: participant.id,
                        firstName: participant.firstName || '',
                        lastName: participant.lastName || '',
                        ecclesiasticalRole: participant.ecclesiasticalRole,
                        email: participant.email,
                        avatar: participant.avatar,
                    },
                    attended: state.status === 'asistio',
                    notes: state.status === 'no_asistio' ? (state.reason || '') : '',
                    partialAttendance: state.status === 'asistio' ? (state.isPartial || false) : false,
                    partialAttendanceComment: state.partialComment || '',
                    timeStayed: state.timeStayed || '',
                }
            })
    }, [event, participantStates])

    /** Actualiza el evento con nuevos registros de asistencia */
    const updateEventWithAttendance = useCallback(async (attendanceRecords: any[], sessionStatus?: string) => {
        if (!eventId || !sessionId || !event) return null
        
        const updatedSessions = event.sessions.map(session => {
            if (session.id.toString() === sessionId) {
                return { 
                    ...session, 
                    attendanceRecords, 
                    status: sessionStatus || (session.status === 'pendiente' ? 'en_progreso' : session.status)
                }
            }
            return session
        })
        
        const shouldUpdateEventStatus = event.eventStatus === 'programada' || sessionStatus === 'completada'
        const newEventStatus = sessionStatus === 'completada'
            ? (updatedSessions.every(s => s.status === 'completada') ? 'completado' : 'en-progreso')
            : (event.eventStatus === 'programada' ? 'en-progreso' : event.eventStatus)

        return await EventsService.updateEvent(eventId, {
            eventStatus: shouldUpdateEventStatus ? newEventStatus : event.eventStatus,
            sessions: updatedSessions
        })
    }, [eventId, sessionId, event])

    /** Guarda el progreso de la asistencia sin finalizar la sesión */
    const handleSaveProgress = useCallback(async () => {
        if (!eventId || !sessionId || !event) return
        
        setSaving(true)
        setError(null)
        
        try {
            const attendanceRecords = buildRecords()
            const updatedEvent = await updateEventWithAttendance(attendanceRecords)
            
            if (updatedEvent) {
                setEvent(updatedEvent)
                toast.push('Progreso guardado: El progreso de la asistencia ha sido guardado correctamente.')
            }
        } catch (error) {
            const errorType = handleError(error, 'guardar progreso')
            toast.push(`Error al guardar: ${ERROR_CONFIG[errorType].message}`)
        } finally {
            setSaving(false)
        }
    }, [eventId, sessionId, event, buildRecords, updateEventWithAttendance, handleError])

    /** Valida todos los participantes antes de finalizar */
    const validateParticipants = useCallback(() => {
        if (!event?.participantsInvited) return true
        
        let allValid = true
        
        for (const participant of event.participantsInvited) {
            const participantId = participant.id.toString()
            const state = participantStates[participantId] || {}
            const dataToValidate = { 
                status: state.status, 
                isPartial: state.isPartial, 
                timeStayed: state.timeStayed 
            }
            
            try {
                validationSchemas.finalize.validateSync(dataToValidate, { abortEarly: false })
            } catch (error) {
                allValid = false
                if (error instanceof Yup.ValidationError) {
                    error.inner.forEach(validationError => {
                        toast.push(
                            `Error en ${participant.firstName} ${participant.lastName}: ${validationError.message}`, 
                            { placement: 'top-center' }
                        )
                    })
                }
            }
        }
        
        return allValid
    }, [event, participantStates, validationSchemas.finalize])

    /** Guarda el progreso y marca la sesión como completada */
    const handleSaveAndFinalize = useCallback(async () => {
        if (!eventId || !sessionId || !event) return
        
        setSaving(true)
        setError(null)
        
        // Validar participantes
        if (!validateParticipants()) {
            setSaving(false)
            return
        }
        
        try {
            const attendanceRecords = buildRecords()
            const updatedEvent = await updateEventWithAttendance(attendanceRecords, 'completada')
            
            if (updatedEvent) {
                setEvent(updatedEvent)
                toast.push('La asistencia ha sido registrada y la sesión ha sido marcada como completada.')
                navigateBack()
            }
        } catch (error) {
            const errorType = handleError(error, 'finalizar asistencia')
            toast.push(`Error al finalizar: ${ERROR_CONFIG[errorType].message}`)
        } finally {
            setSaving(false)
        }
    }, [eventId, sessionId, event, validateParticipants, buildRecords, updateEventWithAttendance, handleError, navigateBack])

    /** Interface pública utilizada por las vistas de asistencia */
    return {
        event,
        participantStates,
        loading,
        saving,
        error,
        updateParticipantState,
        handleAttendanceStatusChange,
        handleReasonChange,
        handlePartialAttendanceChange,
        handlePartialCommentChange,
        handleTimeStayedChange,
        handleSaveProgress,
        handleSaveAndFinalize,
        getBackButtonText,
        navigateBack,
        participantProgressSchema: validationSchemas.progress,
        participantFinalizeSchema: validationSchemas.finalize,
    }
}

export default useAttendance
