import { describe, it, expect } from 'vitest'
import { ensureParticipantIds, sanitizeEvent } from '../eventDataUtils'
import type { Event } from '../../types'

const baseParticipant = {
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
} as any

describe('ensureParticipantIds', () => {
    it('asigna ids cuando faltan', () => {
        const participants = [ { ...baseParticipant } ]
        const result = ensureParticipantIds(participants)
        expect(result[0].id).toBeTruthy()
    })

    it('mantiene ids existentes', () => {
        const participants = [ { ...baseParticipant, id: 'abc' } ]
        const result = ensureParticipantIds(participants)
        expect(result[0].id).toBe('abc')
    })
})

describe('sanitizeEvent', () => {
    const event: Event = {
        id: 1,
        title: 'Test',
        date: new Date('2023-01-01'),
        startTime: new Date('2023-01-01T10:00:00'),
        location: 'X',
        status: 'programada',
        participantsInvited: [ { ...baseParticipant } ],
        sessions: [
            {
                id: 's1',
                date: new Date('2023-01-01'),
                attendanceMode: 'normal',
                isDefault: true,
                status: 'pendiente',
                attendanceRecords: []
            }
        ]
    }

    it('normaliza fechas y participantes', () => {
        const sanitized = sanitizeEvent(event)
        expect(sanitized.date).toBe('2023-01-01')
        expect(sanitized.startTime).toBe('10:00')
        expect(sanitized.participantsInvited[0].id).toBeTruthy()
        expect(sanitized.sessions[0].date).toBe('2023-01-01')
    })
})
