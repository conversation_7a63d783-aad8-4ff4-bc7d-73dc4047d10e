/**
 * Vista para acceso directo a registrar asistencia
 * Muestra una lista de eventos activos para seleccionar y registrar asistencia
 */
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Tooltip from '@/shared/components/ui/Tooltip'
import Loading from '@/shared/components/shared/Loading'
import {
    HiCalendar,
    HiClock,
    HiLocationMarker,
    HiUserGroup,
    HiArrowLeft,
    HiBriefcase,
    HiDownload
} from 'react-icons/hi'
// Ya no necesitamos importar mockEvents porque usamos EventsService
import type { Event } from '../../types'
import StatusBadge from '../../components/StatusBadge'
import SelectSessionModal from '../../components/SelectSessionModal'
import EventsService from '../../services/EventsService'
import toast from '@/shared/components/ui/toast/toast'
import Notification from '@/shared/components/ui/Notification'
import exportEventParticipants from '../../utils/exportEventParticipants.tsx'
import Badge from '@/shared/components/ui/Badge'
import EventDateRange from '../../components/EventDateRange'
import EventTimeInfo from '../../components/EventTimeInfo'

/**
 * Componente para acceso directo a registrar asistencia
 */
const DirectAttendanceShortcutView = () => {
    // Estado para almacenar los eventos activos
    const [activeEvents, setActiveEvents] = useState<Event[]>([])

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)
    
    // Estado para mensajes de error
    const [error, setError] = useState<string | null>(null)

    // Estados para el modal de selección de sesiones
    const [isSessionModalOpen, setSessionModalOpen] = useState(false)
    const [selectedEventForModal, setSelectedEventForModal] = useState<Event | null>(null)

    // Hook de navegación
    const navigate = useNavigate()

    // Hook de navegación contextual
    const { navigateToAttendance } = useNavigationContext()

    // Cargar los eventos activos al montar el componente
    useEffect(() => {
        const fetchActiveEvents = async () => {
            try {
                setLoading(true)
                setError(null)

                // Obtener todos los eventos
                const events = await EventsService.getEvents()

                // Filtrar eventos con estado 'programada' o 'en-progreso'
                const active = events.filter(
                    event => event.eventStatus === 'programada' || event.eventStatus === 'en-progreso'
                )

                setActiveEvents(active)
                setLoading(false)
            } catch (error) {
                console.error('Error al cargar los eventos activos:', error)
                setError('Ocurrió un error al cargar los eventos activos. Por favor, inténtelo de nuevo.')
                setLoading(false)
                
                // Mostrar notificación de error
                toast.push(
                    <Notification title="Error" type="danger">
                        {error instanceof Error ? error.message : 'Ocurrió un error al cargar los eventos activos. Por favor, inténtelo de nuevo.'}
                    </Notification>
                )
            }
        }

        fetchActiveEvents()
    }, [])

    // Navegar a la vista de registro de asistencia para el evento seleccionado
    // Decide entre mostrar modal de sesiones o ir directo según el número de sesiones
    const handleSelectEventForAttendance = (eventId: string | number) => {
        // Buscar el evento en la lista de eventos activos
        const selectedEvent = activeEvents.find(event => event.id.toString() === eventId.toString())

        console.log('🔍 DEBUG DirectAttendance - handleSelectEventForAttendance called for eventId:', eventId)
        console.log('🔍 DEBUG DirectAttendance - Selected event:', selectedEvent?.title)
        console.log('🔍 DEBUG DirectAttendance - Event sessions:', selectedEvent?.sessions)
        console.log('🔍 DEBUG DirectAttendance - Sessions length:', selectedEvent?.sessions?.length)

        if (!selectedEvent) return

        // Si el evento tiene más de una sesión, mostrar modal de selección
        if (selectedEvent.sessions && selectedEvent.sessions.length > 1) {
            console.log('🔍 DEBUG DirectAttendance - Multiple sessions detected, showing modal')
            setSelectedEventForModal(selectedEvent)
            setSessionModalOpen(true)
        } else {
            console.log('🔍 DEBUG DirectAttendance - Single session or no sessions, navigating directly')
            // Si tiene una sola sesión, ir directamente según la configuración de la sesión
            const session = selectedEvent.sessions?.[0]
            if (session) {
                console.log('🔍 DEBUG DirectAttendance - Session found:', session)
                if (session.attendanceMode === 'kiosk') {
                    navigateToAttendance(
                        `/events/${eventId}/sessions/${session.id}/asistencia-rapida`,
                        'direct-attendance',
                        '/events/direct-attendance',
                        eventId.toString(),
                        session.id.toString()
                    )
                } else {
                    navigateToAttendance(
                        `/events/${eventId}/sessions/${session.id}/asistencia`,
                        'direct-attendance',
                        '/events/direct-attendance',
                        eventId.toString(),
                        session.id.toString()
                    )
                }
            } else {
                // Si no hay sesiones, esto no debería ocurrir, pero manejamos el caso
                console.warn('Evento sin sesiones, esto no debería ocurrir')
                // Crear una navegación de fallback
                navigate(`/events/${eventId}/attendance`)
            }
        }
    }

    // Volver a la lista de eventos
    const handleBack = () => {
        navigate('/events/list')
    }

    // Ya no necesitamos funciones locales de formateo porque usamos componentes reutilizables

    // Renderizar componente de carga
    if (loading) {
        return (
            <div className="container mx-auto p-4">
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center">
                        <Button
                            className="mr-4"
                            variant="default"
                            size="sm"
                            icon={<HiArrowLeft />}
                            onClick={handleBack}
                        >
                            Volver
                        </Button>
                        <h1 className="text-2xl font-bold">Registrar Asistencia</h1>
                    </div>
                </div>
                <Loading loading={loading} />
            </div>
        )
    }

    // Renderizar mensaje de error
    if (error) {
        return (
            <div className="container mx-auto p-4">
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center">
                        <Button
                            className="mr-4"
                            variant="default"
                            size="sm"
                            icon={<HiArrowLeft />}
                            onClick={handleBack}
                        >
                            Volver
                        </Button>
                        <h1 className="text-2xl font-bold">Registrar Asistencia</h1>
                    </div>
                </div>
                <Card>
                    <div className="text-center p-8">
                        <p className="text-red-500 mb-4">{error}</p>
                        <Button onClick={() => window.location.reload()}>Reintentar</Button>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            {/* Encabezado */}
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        variant="default"
                        size="sm"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                    >
                        Volver
                    </Button>
                    <h1 className="text-2xl font-bold">Registrar Asistencia</h1>
                </div>
            </div>

            {/* Contenido principal */}
            {activeEvents.length === 0 ? (
                <Card>
                    <div className="text-center p-8">
                        <h3 className="text-lg font-semibold mb-2">No hay eventos activos</h3>
                        <p className="text-gray-600">No hay eventos programados o en progreso en este momento.</p>
                    </div>
                </Card>
            ) : (
                <div>
                    <p className="text-gray-600 mb-4">
                        Selecciona un evento para registrar la asistencia:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {activeEvents.map((event) => (
                            <Card key={event.id} className="hover:shadow-lg transition-shadow">
    <div className="p-6 flex flex-col h-full">
        {/* Título */}
        <h3 className="text-lg font-semibold text-gray-800 mb-2 w-full">
            {event.title}
        </h3>
        {/* Estado alineado derecha */}
        <div className="flex justify-end mb-3">
            <StatusBadge status={event.eventStatus} />
        </div>
        {/* Subject */}
        <p className="text-sm text-gray-600 mb-3">
            {event.subject}
        </p>
        {/* Fecha y hora */}
        <div className="flex flex-wrap gap-3 mb-2">
            <div className="flex items-center text-sm text-gray-600">
                <HiCalendar className="text-lg mr-2" />
                <EventDateRange startDate={event.date} endDate={event.endDate} />
            </div>
            <div className="flex items-center text-sm text-gray-600">
                <HiClock className="text-lg mr-2" />
                <EventTimeInfo
                    startDate={event.date}
                    endDate={event.endDate}
                    startTime={event.startTime}
                    endTime={event.endTime}
                />
            </div>
        </div>
        {/* Ubicación */}
        <div className="flex items-center text-sm text-gray-600 mb-2">
            <HiLocationMarker className="text-lg mr-2" />
            <span>{event.location}</span>
        </div>
        {/* Participantes */}
        <div className="flex items-center text-sm text-gray-600 mb-2">
            <HiUserGroup className="text-lg mr-2" />
            <span>{event.participantsInvited?.length || 0} participantes</span>
        </div>
        {event.sessions && event.sessions.length > 1 && (
            <div className="flex items-center text-sm text-gray-600 mb-2">
                <Badge
                    content={event.sessions.length}
                    className="mr-2 bg-blue-100 text-blue-800"
                />
                <span>{event.sessions.length} sesiones de asistencia</span>
            </div>
        )}
        {/* Departamento invitador */}
        {event.invitingDepartment && (
            <div className="flex items-center text-sm text-gray-600 mb-4">
                <HiBriefcase className="text-lg mr-2" />
                <span>Departamento: {event.invitingDepartment}</span>
            </div>
        )}
        <div className="flex justify-end mt-auto space-x-2">
            <Tooltip title="Descargar participantes">
                <Button
                    size="sm"
                    variant="plain"
                    icon={<HiDownload />}
                    onClick={() => exportEventParticipants(event)}
                />
            </Tooltip>
            <Button
                variant="solid"
                size="sm"
                onClick={() => handleSelectEventForAttendance(event.id)}
            >
                Seleccionar Evento
            </Button>
        </div>
    </div>
</Card>
                        ))}
                    </div>
                </div>
            )}

            {/* Modal de selección de sesiones */}
            <SelectSessionModal
                isOpen={isSessionModalOpen}
                onClose={() => setSessionModalOpen(false)}
                event={selectedEventForModal}
                navigationOrigin="direct-attendance"
                returnPath="/events/direct-attendance"
            />
        </div>
    )
}

export default DirectAttendanceShortcutView
