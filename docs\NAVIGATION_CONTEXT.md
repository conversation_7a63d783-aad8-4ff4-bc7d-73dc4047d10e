# Sistema de Navegación Contextual

## Descripción

El sistema de navegación contextual permite que las páginas de asistencia por sesión regresen al origen correcto según desde dónde llegó el usuario, proporcionando una experiencia de navegación más intuitiva y coherente.

## Funcionamiento

### Hook `useNavigationContext`

El hook `useNavigationContext` maneja el tracking del origen de navegación y proporciona funciones para navegar de vuelta al origen correcto.

#### Tipos de Origen

- `'events-list'`: Usuario llegó desde la lista de eventos (`/events/list`)
- `'direct-attendance'`: Usuario llegó desde la página de asistencia directa (`/events/direct-attendance`)
- `'event-detail'`: Usuario llegó desde la página de detalles del evento (`/events/{id}`)
- `'attendance'`: Usuario llegó desde la página de registro de asistencia normal (`/events/{id}/sessions/{sessionId}/asistencia`)
- `'fast-attendance'`: Usuario llegó desde la página de registro de asistencia rápida (`/events/{id}/sessions/{sessionId}/fast-attendance`)
- `'unknown'`: Acceso directo por URL sin navegación previa

#### Funciones Principales

- `navigateToAttendance()`: Navega a una página de asistencia preservando el contexto
- `navigateBack()`: Navega de vuelta al origen según el contexto
- `getBackButtonText()`: Obtiene el texto apropiado para el botón de volver

### Flujo de Navegación

1. **Desde Lista de Eventos** (`/events/list`):
   - Al hacer clic en "Registrar Asistencia" → Navega con contexto `'events-list'`
   - En página de asistencia, botón "Volver" → Regresa a `/events/list`

2. **Desde Asistencia Directa** (`/events/direct-attendance`):
   - Al seleccionar un evento → Navega con contexto `'direct-attendance'`
   - En página de asistencia, botón "Volver" → Regresa a `/events/direct-attendance`

3. **Desde Detalles de Evento** (`/events/{id}`):
   - Al hacer clic en "Registrar Asistencia" → Navega con contexto `'event-detail'`
   - En página de asistencia, botón "Volver" → Regresa a `/events/{id}`

4. **Acceso Directo por URL**:
   - Sin contexto previo → Usa fallback a `/events/direct-attendance`

5. **Desde Registro de Asistencia** (`/events/{id}/sessions/{sessionId}/asistencia`):
   - Al hacer clic en "Registrar nuevo participante" → Navega con contexto `'attendance'`
   - En página de registro, botón "Volver a Registro de Asistencia" → Regresa a la página de asistencia

6. **Desde Asistencia Rápida** (`/events/{id}/sessions/{sessionId}/fast-attendance`):
   - Al hacer clic en "Registrar nuevo participante" → Navega con contexto `'fast-attendance'`
   - En página de registro, botón "Volver a Asistencia Rápida" → Regresa a la página de asistencia rápida

### Componentes Actualizados

#### Páginas de Origen
- `EventListView.tsx`: Pasa contexto `'events-list'`
- `DirectAttendanceShortcutView.tsx`: Pasa contexto `'direct-attendance'`
- `EventDetailView.tsx`: Pasa contexto `'event-detail'`

#### Componentes Intermedios
- `SelectSessionModal.tsx`: Preserva y pasa el contexto de navegación

#### Páginas de Destino
- `AttendanceView.tsx`: Usa navegación contextual para el botón "Volver" y pasa contexto al registro de participantes
- `FastAttendanceView.tsx`: Usa navegación contextual para el botón "Volver" y pasa contexto al registro de participantes
- `ParticipantRegistrationView.tsx`: Usa navegación contextual para regresar después del registro

### Implementación Técnica

El contexto se pasa a través del `state` de React Router:

```typescript
navigate(path, {
    state: {
        navigationContext: {
            origin: 'events-list',
            returnPath: '/events/list',
            eventId: '123',
            sessionId: '456'
        }
    }
})
```

### Textos de Botones

Los botones "Volver" muestran texto contextual:
- Desde lista de eventos: "Volver a Lista"
- Desde asistencia directa: "Volver a Asistencia"
- Desde detalles de evento: "Volver a Detalles"
- Desde registro de asistencia: "Volver a Registro de Asistencia"
- Desde asistencia rápida: "Volver a Asistencia Rápida"
- Sin contexto: "Volver"

## Beneficios

1. **Experiencia de Usuario Mejorada**: Los usuarios regresan al lugar correcto
2. **Navegación Intuitiva**: El comportamiento es predecible y coherente
3. **Flexibilidad**: Funciona con múltiples puntos de entrada
4. **Fallback Robusto**: Maneja casos de acceso directo por URL
5. **Mantenibilidad**: Código centralizado y reutilizable

## Corrección del Problema del Modal

### Problema Original
Cuando se usaba el `SelectSessionModal`, el contexto de navegación no se preservaba correctamente porque el modal intentaba obtener el contexto desde `location.state`, pero cuando se abría desde la misma página no había contexto en el state.

### Solución Implementada
1. **Modificación de Props**: Se agregaron props opcionales `navigationOrigin` y `returnPath` al `SelectSessionModal`
2. **Paso de Contexto**: Las páginas que abren el modal ahora pasan explícitamente el contexto de navegación
3. **Fallback Robusto**: Si no se proporciona contexto, el modal usa navegación normal como fallback

### Componentes Actualizados para la Corrección
- `SelectSessionModal.tsx`: Recibe contexto como props en lugar de intentar obtenerlo desde location.state
- `EventListView.tsx`: Pasa `navigationOrigin="events-list"` y `returnPath="/events/list"`
- `DirectAttendanceShortcutView.tsx`: Pasa `navigationOrigin="direct-attendance"` y `returnPath="/events/direct-attendance"`

## Casos de Prueba Recomendados

### Escenario 1: Desde Lista de Eventos con Modal
1. Ir a `/events/list`
2. Hacer clic en "Registrar Asistencia" de un evento con múltiples sesiones
3. Verificar que se abre el modal de selección de sesiones
4. Seleccionar una sesión específica
5. Verificar que navega a la página de asistencia correspondiente
6. Hacer clic en "Volver a Lista"
7. **Resultado esperado**: Debe regresar a `/events/list`

### Escenario 2: Desde Asistencia Directa con Modal
1. Ir a `/events/direct-attendance`
2. Hacer clic en "Seleccionar Reunión" de un evento con múltiples sesiones
3. Verificar que se abre el modal de selección de sesiones
4. Seleccionar una sesión específica
5. Verificar que navega a la página de asistencia correspondiente
6. Hacer clic en "Volver a Asistencia"
7. **Resultado esperado**: Debe regresar a `/events/direct-attendance`

### Escenario 3: Navegación Directa sin Modal
1. Ir a `/events/list`
2. Hacer clic en "Registrar Asistencia" de un evento con una sola sesión
3. Verificar que navega directamente a la página de asistencia (sin modal)
4. Hacer clic en "Volver a Lista"
5. **Resultado esperado**: Debe regresar a `/events/list`

### Escenario 4: Acceso Directo por URL
1. Navegar directamente a `/events/{id}/sessions/{sessionId}/asistencia`
2. Hacer clic en "Volver"
3. **Resultado esperado**: Debe ir a `/events/direct-attendance` (fallback)
