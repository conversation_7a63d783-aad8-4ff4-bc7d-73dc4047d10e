import { describe, it, expect, vi, beforeEach } from 'vitest'
import ParticipantsService from '../ParticipantsService'
import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type { Participant, ParticipantResponse, CreateParticipantRequest } from '../../types'

/**
 * Pruebas unitarias para ParticipantsService
 *
 * Estas pruebas validan la funcionalidad del servicio de participantes,
 * incluyendo la estrategia de populate jerárquico, filtros con documentId,
 * y el manejo dual de estructuras de datos para Strapi v5.
 */

// Mock del ApiService para simular respuestas de la API
vi.mock('@/shared/services/ApiService', () => ({
    default: {
        fetchData: vi.fn()
    }
}))

const mockApiService = vi.mocked(ApiService)

describe('ParticipantsService', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    describe('getParticipants', () => {
        it('debe obtener participantes con populate de relaciones jerárquicas', async () => {
            // Arrange
            const mockParticipants: Participant[] = [
                {
                    id: 1,
                    documentId: 'doc-1',
                    firstName: 'Juan',
                    lastName: 'Pérez',
                    email: '<EMAIL>',
                    phone: '123456789',
                    ecclesiasticalRole: 'Pastor',
                    fieldAssignment: {
                        id: 1,
                        documentId: 'field-1',
                        name: 'Asociación Central',
                        acronym: 'ACD',
                        type: 'Asociación'
                    },
                    zoneAssignment: {
                        id: 1,
                        documentId: 'zone-1',
                        name: 'Zona Central',
                        field: {
                            id: 1,
                            documentId: 'field-1',
                            name: 'Asociación Central',
                            acronym: 'ACD',
                            type: 'Asociación'
                        }
                    },
                    districtAssignment: {
                        id: 1,
                        documentId: 'district-1',
                        name: 'Distrito Central',
                        zone: {
                            id: 1,
                            documentId: 'zone-1',
                            name: 'Zona Central'
                        }
                    },
                    churchAssignment: {
                        id: 1,
                        documentId: 'church-1',
                        name: 'Iglesia Central',
                        district: {
                            id: 1,
                            documentId: 'district-1',
                            name: 'Distrito Central'
                        }
                    }
                }
            ]

            const mockResponse: ParticipantResponse = {
                data: mockParticipants,
                meta: {
                    pagination: {
                        page: 1,
                        pageSize: 25,
                        pageCount: 1,
                        total: 1
                    }
                }
            }

            mockApiService.fetchData.mockResolvedValue({
                data: mockResponse,
                status: 200,
                statusText: 'OK'
            })

            // Act
            const result = await ParticipantsService.getParticipants()

            // Assert
            expect(mockApiService.fetchData).toHaveBeenCalledWith({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'get',
                params: {
                    populate: {
                        avatar: true,
                        userAccount: {
                            populate: ['avatar']
                        },
                        fieldAssignment: {
                            populate: ['*']
                        },
                        zoneAssignment: {
                            populate: ['field']
                        },
                        districtAssignment: {
                            populate: ['zone']
                        },
                        churchAssignment: {
                            populate: ['district']
                        }
                    }
                }
            })

            expect(result).toHaveLength(1)
            expect(result[0]).toMatchObject({
                id: 1,
                firstName: 'Juan',
                lastName: 'Pérez',
                email: '<EMAIL>',
                fieldAssignmentId: 'field-1',
                zoneAssignmentId: 'zone-1',
                districtAssignmentId: 'district-1',
                churchAssignmentId: 'church-1'
            })
        })

        it('debe aplicar filtros correctamente usando documentId', async () => {
            // Arrange
            const filters = {
                search: 'Juan',
                fieldAssignmentId: 'field-1',
                zoneAssignmentId: 'zone-1',
                districtAssignmentId: 'district-1',
                churchAssignmentId: 'church-1',
                ecclesiasticalRole: 'Pastor'
            }

            mockApiService.fetchData.mockResolvedValue({
                data: { data: [], meta: { pagination: { page: 1, pageSize: 25, pageCount: 0, total: 0 } } },
                status: 200,
                statusText: 'OK'
            })

            // Act
            await ParticipantsService.getParticipants(filters)

            // Assert
            expect(mockApiService.fetchData).toHaveBeenCalledWith({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'get',
                params: {
                    populate: expect.any(Object),
                    filters: {
                        $or: [
                            { firstName: { $containsi: 'Juan' } },
                            { lastName: { $containsi: 'Juan' } },
                            { email: { $containsi: 'Juan' } },
                            { ecclesiasticalRole: { $containsi: 'Juan' } }
                        ],
                        fieldAssignment: { documentId: 'field-1' },
                        zoneAssignment: { documentId: 'zone-1' },
                        districtAssignment: { documentId: 'district-1' },
                        churchAssignment: { documentId: 'church-1' },
                        ecclesiasticalRole: { $eq: 'Pastor' }
                    }
                }
            })
        })
    })

    describe('createParticipant', () => {
        it('debe crear un participante con relaciones jerárquicas usando IDs', async () => {
            // Arrange
            const participantData: CreateParticipantRequest = {
                firstName: 'María',
                lastName: 'González',
                email: '<EMAIL>',
                phone: '*********',
                ecclesiasticalRole: 'Anciano',
                fieldAssignment: 'field-1',
                zoneAssignment: 'zone-1',
                districtAssignment: 'district-1',
                churchAssignment: 'church-1'
            }

            const mockCreatedParticipant: Participant = {
                id: 2,
                documentId: 'doc-2',
                ...participantData,
                fieldAssignment: {
                    id: 1,
                    documentId: 'field-1',
                    name: 'Asociación Central',
                    acronym: 'ACD',
                    type: 'Asociación'
                }
            }

            mockApiService.fetchData.mockResolvedValue({
                data: { data: mockCreatedParticipant },
                status: 201,
                statusText: 'Created'
            })

            // Act
            const result = await ParticipantsService.createParticipant(participantData)

            // Assert
            expect(mockApiService.fetchData).toHaveBeenCalledWith({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'post',
                data: {
                    data: {
                        firstName: 'María',
                        lastName: 'González',
                        email: '<EMAIL>',
                        phone: '*********',
                        ecclesiasticalRole: 'Anciano',
                        fieldAssignment: 'field-1',
                        zoneAssignment: 'zone-1',
                        districtAssignment: 'district-1',
                        churchAssignment: 'church-1',
                        userAccount: null
                    }
                }
            })

            expect(result).toEqual(mockCreatedParticipant)
        })
    })

    describe('findParticipantByEmail', () => {
        it('debe buscar un participante por email con populate completo', async () => {
            // Arrange
            const email = '<EMAIL>'
            const mockParticipant: Participant = {
                id: 1,
                documentId: 'doc-1',
                firstName: 'Test',
                lastName: 'User',
                email: email,
                ecclesiasticalRole: 'Miembro'
            }

            mockApiService.fetchData.mockResolvedValue({
                data: { data: [mockParticipant] },
                status: 200,
                statusText: 'OK'
            })

            // Act
            const result = await ParticipantsService.findParticipantByEmail(email)

            // Assert
            expect(mockApiService.fetchData).toHaveBeenCalledWith({
                url: API_ENDPOINTS.PARTICIPANTS.BASE,
                method: 'get',
                params: {
                    filters: {
                        email: { $eq: email }
                    },
                    populate: expect.any(Object)
                }
            })

            expect(result).toEqual(mockParticipant)
        })

        it('debe retornar null si no encuentra el participante', async () => {
            // Arrange
            mockApiService.fetchData.mockResolvedValue({
                data: { data: [] },
                status: 200,
                statusText: 'OK'
            })

            // Act
            const result = await ParticipantsService.findParticipantByEmail('<EMAIL>')

            // Assert
            expect(result).toBeNull()
        })
    })
})
