/**
 * Vista de formulario para crear o editar un evento
 */
import React, { useState, useEffect, useRef } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { FormContainer, FormItem } from '@/shared/components/ui/Form'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Card from '@/shared/components/ui/Card'
import Select from '@/shared/components/ui/Select'
import DatePicker from '@/shared/components/ui/DatePicker'
import TimeInput from '@/shared/components/ui/TimeInput'
import Checkbox from '@/shared/components/ui/Checkbox'
import Dialog from '@/shared/components/ui/Dialog'
import ConfirmDialog from '@/shared/components/shared/ConfirmDialog'
import Avatar from '@/shared/components/ui/Avatar'
import { parseTimeString } from '../../utils/timeUtils'
import Upload from '@/shared/components/ui/Upload'
import Switcher from '@/shared/components/ui/Switcher'
import Radio from '@/shared/components/ui/Radio'
import {
    HiArrowLeft,
    HiSave,
    HiOutlineDocumentDownload,
    HiUserAdd,
    HiXCircle,
    HiSearch,
    HiRefresh,
    HiInformationCircle
} from 'react-icons/hi'
import { Field, Form, Formik, useFormikContext } from 'formik'
import * as Yup from 'yup'
import * as XLSX from 'xlsx'
import { mockMeetings as mockEvents } from '@/mock/data/eventsData/index'
import { mockMeetingParticipants, mockHierarchicalData } from '@/mock/data/churchUsersData'
import ParticipantsService from '../../services/ParticipantsService'
import type {
    Event,
    Participant,
    EventStatus,
    EventType,
    HierarchicalFilterData,
    Department
} from '../../types'
import toast from '@/shared/components/ui/toast'
import Notification from '@/shared/components/ui/Notification'
import EventsService from '../../services/EventsService'
import DepartmentsService from '../../services/DepartmentsService'
import SessionTable from '../../components/SessionTable'

// Componente que sincroniza campos según el tipo seleccionado
const TypeWatcher = ({ eventTypes }: { eventTypes: EventType[] }) => {
    const { values, setFieldValue } = useFormikContext<any>()
    const [previousTypeId, setPreviousTypeId] = useState(values.typeId)

    useEffect(() => {
        const selected = eventTypes.find(
            (t) => t.id.toString() === values.typeId?.toString(),
        )

        // Aplicar valores por defecto cuando cambia el tipo de evento
        if (selected && values.typeId !== previousTypeId) {
            // Siempre establecer los valores por defecto del tipo seleccionado
            setFieldValue('autoRegistration', selected.autoRegistrationDefault)

            // Aplicar el método de asistencia según el modo actual
            if (values.hasMultipleSessions) {
                // Modo avanzado: Aplicar a todas las sesiones
                if (values.sessions && values.sessions.length > 0) {
                    const attendanceMode = selected.attendanceMethodDefault === 'kiosco_rapido' ? 'kiosk' : 'normal'
                    const updatedSessions = values.sessions.map((session: any) => ({
                        ...session,
                        attendanceMode
                    }))
                    setFieldValue('sessions', updatedSessions)
                }
            } else {
                // Modo simple: Actualizar campo de modo de asistencia y sesión por defecto
                const attendanceMethodValue = selected.attendanceMethodDefault || 'manual'
                setFieldValue('attendanceMode', attendanceMethodValue)

                // Actualizar la sesión por defecto
                if (values.sessions && values.sessions.length > 0) {
                    const attendanceMode = attendanceMethodValue === 'kiosco_rapido' ? 'kiosk' : 'normal'
                    const updatedSessions = values.sessions.map((session: any) => {
                        if (session.isDefault) {
                            return {
                                ...session,
                                attendanceMode
                            }
                        }
                        return session
                    })
                    setFieldValue('sessions', updatedSessions)
                }
            }

            // Resetear las banderas de modificación manual para permitir nuevas modificaciones
            setFieldValue('_userModifiedAutoRegistration', false)

            setPreviousTypeId(values.typeId)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values.typeId])

    return null
}

// Componente que sincroniza la fecha del evento con la sesión por defecto
const DateSyncWatcher = () => {
    const { values, setFieldValue } = useFormikContext<any>()
    const [previousDate, setPreviousDate] = useState(values.date)

    useEffect(() => {
        // Sincronizar fecha del evento con la sesión por defecto cuando cambia la fecha
        if (values.date !== previousDate && values.sessions && values.sessions.length > 0) {
            const updatedSessions = values.sessions.map((session: any) => {
                // Solo actualizar la fecha de la sesión por defecto
                if (session.isDefault) {
                    return {
                        ...session,
                        date: values.date
                    }
                }
                return session
            })
            setFieldValue('sessions', updatedSessions)
            setPreviousDate(values.date)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [values.date])

    return null
}

// Esquema de validación para el formulario
const validationSchema = Yup.object().shape({
    title: Yup.string().required('El título es obligatorio'),
    date: Yup.string().required('La fecha de inicio es obligatoria'),
    endDate: Yup.string().when('isMultiDay', {
        is: true,
        then: (schema) => schema.required('La fecha de fin es obligatoria para eventos de múltiples días')
            .test('is-after-start', 'La fecha de fin debe ser posterior a la fecha de inicio', function(value) {
                const { date } = this.parent;
                if (!value || !date) return true;
                return new Date(value) >= new Date(date);
            }),
        otherwise: (schema) => schema.notRequired()
    }),
    isMultiDay: Yup.boolean(),
    hasMultipleSessions: Yup.boolean(),
    sessions: Yup.array().when('hasMultipleSessions', {
        is: true,
        then: (schema) => schema.min(2, 'Para eventos con múltiples sesiones debe haber al menos 2 sesiones. Desactive el toggle o agregue más sesiones.'),
        otherwise: (schema) => schema.min(1, 'Debe haber al menos una sesión')
    }),
    startTime: Yup.string().required('La hora de inicio es obligatoria'),
    endTime: Yup.string().required('La hora de finalización es obligatoria'),
    location: Yup.string().required('La ubicación es obligatoria'),
    eventStatus: Yup.string().required('El estado es obligatorio'),
})

// Opciones para el estado del evento
const statusOptions = [
    { value: 'programada', label: 'Programada' },
    { value: 'en-progreso', label: 'En Progreso' },
    { value: 'completada', label: 'Completada' },
    { value: 'cancelada', label: 'Cancelada' },
]

/**
 * Componente para el formulario de creación/edición de eventos
 */
const EventFormView = () => {
    // Obtener el ID del evento de los parámetros de la URL (si existe)
    const { id } = useParams<{ id: string }>()

    // Estado para determinar si es una edición o creación
    const isEditMode = Boolean(id)

    // Estado para almacenar el evento a editar
    const [eventData, setEventData] = useState<Event | null>(null)

    // Estado para los participantes seleccionados
    const [selectedParticipants, setSelectedParticipants] = useState<Participant[]>([])

    // Lista de tipos de evento disponibles
    const [eventTypes, setEventTypes] = useState<EventType[]>([])

    // Lista de departamentos disponibles
    const [departments, setDepartments] = useState<Department[]>([])

    // Estado para el modal de añadir participantes
    const [isModalOpen, setIsModalOpen] = useState(false)
    // Estado para el modal de importar participantes
    const [isImportModalOpen, setisImportModalOpen] = useState(false)
    // Estado para el modal de confirmación de eliminación de sesiones
    const [showSessionDeleteConfirm, setShowSessionDeleteConfirm] = useState(false)
    // Estado temporal para el valor del toggle mientras se confirma
    const [pendingToggleValue, setPendingToggleValue] = useState(false)
    // Referencia a Formik para acceder desde el modal de confirmación
    const formikRef = useRef<any>(null)

    // Estados para los filtros del modal
    const [searchTerm, setSearchTerm] = useState('')
    const [selectedField, setSelectedField] = useState<string | number | null>(null)
    const [selectedZone, setSelectedZone] = useState<string | number | null>(null)
    const [selectedDistrict, setSelectedDistrict] = useState<string | number | null>(null)
    const [selectedChurch, setSelectedChurch] = useState<string | number | null>(null)
    const [selectedRole, setSelectedRole] = useState<string | number | null>(null)

    // Estado para los participantes filtrados en el modal
    const [filteredModalParticipants, setFilteredModalParticipants] = useState<Participant[]>([])

    // Estado para los participantes seleccionados en el modal
    const [modalSelectedParticipants, setModalSelectedParticipants] = useState<string[]>([])

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(isEditMode)

    // Estado para controlar la carga de participantes en el modal
    const [loadingParticipants, setLoadingParticipants] = useState(false)

    // Estado para controlar la carga durante la importación de archivos
    const [loadingImport, setLoadingImport] = useState(false)

    // Estado para los datos jerárquicos
    const [hierarchicalData, setHierarchicalData] = useState<{
        fields: Array<{ id: string | number; name: string }>
        zones: Array<{ id: string | number; name: string; fieldId: string | number }>
        districts: Array<{ id: string | number; name: string; zoneId: string | number }>
        churches: Array<{ id: string | number; name: string; districtId: string | number }>
        roles: Array<{ id: string; name: string }>
    } | null>(null)

    // Hook de navegación
    const navigate = useNavigate()

    // Cargar datos jerárquicos al montar el componente
    useEffect(() => {
        const loadHierarchicalData = async () => {
            try {
                const data = await ParticipantsService.getHierarchicalData()
                setHierarchicalData(data)
            } catch (error) {
                console.error('Error al cargar datos jerárquicos:', error)
                // Usar datos mockeados como fallback
                setHierarchicalData({
                    fields: mockHierarchicalData.fields,
                    zones: mockHierarchicalData.zones,
                    districts: mockHierarchicalData.districts,
                    churches: mockHierarchicalData.churches,
                    roles: [
                        { id: 'Pastor', name: 'Pastor' },
                        { id: 'Anciano', name: 'Anciano' },
                        { id: 'Diácono', name: 'Diácono' },
                        { id: 'Departamental', name: 'Departamental' },
                        { id: 'Miembro', name: 'Miembro' }
                    ]
                })
            }
        }

        loadHierarchicalData()
    }, [])

    // Cargar el evento si estamos en modo edición
    useEffect(() => {
        if (isEditMode && id) {
            loadEventForEdit(id)
        }
        const fetchEventTypes = async () => {
            try {
                const types = await EventsService.getEventTypes()
                setEventTypes(types)
            } catch (error) {
                console.error('Error al cargar tipos de evento:', error)
            }
        }

        const fetchDepartments = async () => {
            try {
                const response = await DepartmentsService.getActiveDepartments()
                console.log('Departamentos cargados desde API/Mock:', response.data.length)
                console.log('Total de departamentos disponibles:', response.meta.total)
                setDepartments(response.data)
            } catch (error) {
                console.error('Error al cargar departamentos:', error)
                // En caso de error, se usará el fallback hardcodeado
            }
        }

        fetchEventTypes()
        fetchDepartments()
    }, [isEditMode, id])

    // Cargar datos del evento para editar
    const loadEventForEdit = async (eventId: string) => {
        setLoading(true)

        try {
            // Obtener el evento usando el servicio
            const event = await EventsService.getEventById(eventId)

            if (event) {
                setEventData(event)
                setSelectedParticipants(event.participantsInvited || [])
            }

            setLoading(false)
        } catch (error) {
            console.error('Error al cargar el evento:', error)
            setLoading(false)
        }
    }

    // Opciones de departamentos (con fallback hardcodeado)
    const departmentOptions = React.useMemo(() => {
        if (departments.length > 0) {
            console.log(`Usando ${departments.length} departamentos cargados dinámicamente`)
            // Usar el ID del departamento como value y el nombre como label
            return departments.map(dept => ({ value: dept.id, label: dept.name }))
        }
    }, [departments])

    // Valores iniciales para el formulario
    const getInitialValues = () => {
        if (isEditMode && eventData) {
            return {
                id: eventData.id || '',
                title: eventData.title || '',
                subject: eventData.subject || '',
                topic: eventData.topic || '',
                description: eventData.description || '',
                date: eventData.date || '',
                endDate: eventData.endDate || '',
                isMultiDay: eventData.isMultiDay || false,
                hasMultipleSessions: eventData.hasMultipleSessions || false,
                startTime: parseTimeString(eventData.startTime) || '',
                endTime: parseTimeString(eventData.endTime) || '',
                location: eventData.location || '',
                eventStatus: eventData.eventStatus || 'programada' as EventStatus,
                sendNotifications: eventData.sendNotifications || false,
                invitingDepartment: eventData.invitingDepartment || '',
                typeId: eventData.eventType?.id || '',
                autoRegistration:
                    typeof eventData.autoRegistration === 'boolean'
                        ? eventData.autoRegistration
                        : eventData.eventType?.autoRegistrationDefault || false,
                attendanceMode:
                 typeof eventData.attendanceMethod === 'string'
                    ? eventData.attendanceMethod
                    : eventData.eventType?.attendanceMethodDefault === 'kiosco_rapido'
                    ? 'kiosco_rapido'
                    : 'manual',
                
                // Campos de seguimiento de modificaciones del usuario
                _userModifiedAutoRegistration: false, // Permitir que TypeWatcher establezca valores por defecto
                sessions: eventData.sessions || [
                    {
                        id: 'default_session',
                        date: eventData.date || new Date().toISOString().split('T')[0],
                        comment: 'Registro de asistencia – Por defecto',
                        attendanceMode: 'normal',
                        isDefault: true,
                        status: 'pendiente', // Estado por defecto para nuevas sesiones
                        attendanceRecords: [],
                    },
                ],
            }
        }

        return {
            title: '',
            subject: '',
            topic: '',
            description: '',
            date: '',
            endDate: '',
            isMultiDay: false,
            hasMultipleSessions: false,
            startTime: '',
            endTime: '',
            location: '',
            eventStatus: 'programada' as EventStatus,
            sendNotifications: true,
            invitingDepartment: '',
            typeId: '',
            autoRegistration: false,
            attendanceMode: 'manual',
            // Campos de seguimiento de modificaciones del usuario
            _userModifiedAutoRegistration: false,
            sessions: [
                {
                    id: 'default_session',
                    date: new Date().toISOString().split('T')[0],
                    comment: 'Registro de asistencia – Por defecto',
                    attendanceMode: 'normal',
                    isDefault: true,
                    status: 'pendiente', // Estado por defecto para nuevas sesiones
                    attendanceRecords: [],
                },
            ],
        }
    }

    // Manejar el envío del formulario
    const handleSubmit = async (values: ReturnType<typeof getInitialValues>) => {
        try {
            // Asegurar que la fecha sea un string en formato YYYY-MM-DD
            let dateString = values.date;
            if (values.date instanceof Date) {
                dateString = values.date.toISOString().split('T')[0];
            }

            // Crear objeto de evento asegurando que los participantes se incluyan correctamente
            const { typeId, startTime, endTime, ...rest } = values
            const selectedType = eventTypes.find(
                (t) => t.id.toString() === typeId?.toString(),
            )

            // Convertir startTime y endTime a strings en formato HH:mm
            const formatTimeValue = (timeValue: any): string => {
                if (!timeValue) return ''
                if (typeof timeValue === 'string') return timeValue
                if (timeValue instanceof Date) {
                    return timeValue.toTimeString().slice(0, 5) // HH:mm
                }
                return ''
            }

            const eventToSave: Partial<Event> = {
                ...rest,
                eventType: selectedType,
                date: dateString, // Asegurar que la fecha sea string
                startTime: formatTimeValue(startTime),
                endTime: formatTimeValue(endTime),
                participantsInvited: selectedParticipants, // Aseguramos que los participantes seleccionados se incluyan
                invitingDepartment: values.invitingDepartment.id,
                sessions: values.sessions || [],
            }

            console.log('Guardando evento con participantes:', selectedParticipants.length)

            if (isEditMode && id) {
                // Actualizar evento existente usando el servicio
                const updatedEvent = await EventsService.updateEvent(id, {...eventToSave, id: eventData?.id || ''})

                if (updatedEvent) {
                    console.log('Evento actualizado:', updatedEvent)
                    console.log('Participantes guardados:', updatedEvent.participantsInvited?.length || 0)

                    toast.push(
                        <Notification title="Evento actualizado" type="success">
                            El evento ha sido actualizado correctamente con {selectedParticipants.length} participantes.
                        </Notification>
                    )
                }
            } else {
                // Crear nuevo evento usando el servicio
                const newEvent = await EventsService.createEvent(eventToSave as Omit<Event, 'id'>)

                console.log('Nuevo evento creado:', newEvent)
                console.log('Participantes guardados:', newEvent.participantsInvited?.length || 0)

                toast.push(
                    <Notification title="Evento creado" type="success">
                        El evento ha sido creado correctamente con {selectedParticipants.length} participantes.
                    </Notification>
                )
            }

            // Navegar a la lista de eventos
            navigate('/events/list')
        } catch (error) {
            console.error('Error al guardar el evento:', error)

            toast.push(
                <Notification title="Error" type="danger">
                    Ocurrió un error al guardar el evento. Por favor, inténtelo de nuevo.
                </Notification>
            )
        }
    }

    // Descargar plantilla de Excel para importar participantes
    const handleDownloadTemplate = () => {
        // Crear datos de ejemplo para la plantilla
        const templateData = [
            {
                'Nombre': 'Juan',
                'Apellido': 'Pérez',
                'Email': '<EMAIL>',
                'Teléfono': '+1234567890',
                'Cargo Eclesiástico': 'Pastor',
                'Campo': 'Campo Central',
                'Zona': 'Zona Norte',
                'Distrito': 'Distrito 1',
                'Iglesia': 'Iglesia Central'
            },
            {
                'Nombre': 'María',
                'Apellido': 'González',
                'Email': '<EMAIL>',
                'Teléfono': '+0987654321',
                'Cargo Eclesiástico': 'Anciano',
                'Campo': 'Campo Sur',
                'Zona': 'Zona Sur',
                'Distrito': 'Distrito 2',
                'Iglesia': 'Iglesia del Valle'
            }
        ]

        // Crear libro de trabajo de Excel
        const wb = XLSX.utils.book_new()
        const ws = XLSX.utils.json_to_sheet(templateData)

        // Ajustar el ancho de las columnas
        const colWidths = [
            { wch: 15 }, // Nombre
            { wch: 15 }, // Apellido
            { wch: 25 }, // Email
            { wch: 15 }, // Teléfono
            { wch: 20 }, // Cargo Eclesiástico
            { wch: 15 }, // Campo
            { wch: 15 }, // Zona
            { wch: 15 }, // Distrito
            { wch: 20 }  // Iglesia
        ]
        ws['!cols'] = colWidths

        // Agregar la hoja al libro
        XLSX.utils.book_append_sheet(wb, ws, 'Plantilla Participantes')

        // Descargar el archivo
        XLSX.writeFile(wb, 'plantilla_participantes.xlsx')

        toast.push(
            <Notification title="Plantilla descargada" type="success">
                La plantilla de Excel ha sido descargada correctamente.
            </Notification>
        )
    }

    // Función auxiliar para mapear nombres jerárquicos a IDs
    const mapHierarchicalData = (fieldName?: string, zoneName?: string, districtName?: string, churchName?: string) => {
        const result = {
            fieldAssignmentId: 1, // Valor por defecto
            zoneAssignmentId: 1,
            districtAssignmentId: 1,
            churchAssignmentId: 1
        }

        if (hierarchicalData) {
            // Mapear campo
            if (fieldName) {
                const field = hierarchicalData.fields.find(f =>
                    f.name.toLowerCase() === fieldName.toLowerCase()
                )
                if (field) result.fieldAssignmentId = field.id
            }

            // Mapear zona
            if (zoneName) {
                const zone = hierarchicalData.zones.find(z =>
                    z.name.toLowerCase() === zoneName.toLowerCase()
                )
                if (zone) result.zoneAssignmentId = zone.id
            }

            // Mapear distrito
            if (districtName) {
                const district = hierarchicalData.districts.find(d =>
                    d.name.toLowerCase() === districtName.toLowerCase()
                )
                if (district) result.districtAssignmentId = district.id
            }

            // Mapear iglesia
            if (churchName) {
                const church = hierarchicalData.churches.find(c =>
                    c.name.toLowerCase() === churchName.toLowerCase()
                )
                if (church) result.churchAssignmentId = church.id
            }
        }

        return result
    }

    // Validar y procesar participantes importados desde Excel usando la API real
    const validateAndProcessParticipants = async (importedData: any[]) => {
        const processedParticipants: Participant[] = []
        const errors: string[] = []
        let newUsersCreated = 0
        let existingUsersAdded = 0

        for (let i = 0; i < importedData.length; i++) {
            const row = importedData[i]
            const rowNumber = i + 2 // +2 porque Excel empieza en 1 y hay encabezados

            // Validar campos obligatorios
            if (!row['Email'] || !row['Nombre'] || !row['Apellido']) {
                errors.push(`Fila ${rowNumber}: Email, Nombre y Apellido son obligatorios`)
                continue
            }

            // Validar formato de email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            if (!emailRegex.test(row['Email'])) {
                errors.push(`Fila ${rowNumber}: Formato de email inválido`)
                continue
            }

            try {
                // Buscar usuario existente por email usando la API
                const existingUser = await ParticipantsService.findUserByEmail(row['Email'])

                if (existingUser) {
                    // Usuario existe, solo agregarlo al evento si no está ya seleccionado
                    if (!selectedParticipants.some(p => p.id === existingUser.id)) {
                        processedParticipants.push(existingUser)
                        existingUsersAdded++
                    }
                } else {
                    // Usuario no existe, crear nuevo usuario en el sistema
                    try {
                        // Mapear datos jerárquicos del Excel a IDs
                        const hierarchicalIds = mapHierarchicalData(
                            row['Campo'],
                            row['Zona'],
                            row['Distrito'],
                            row['Iglesia']
                        )

                        const newUser = await ParticipantsService.createUser({
                            email: row['Email'],
                            firstName: row['Nombre'],
                            lastName: row['Apellido'],
                            phone: row['Teléfono'] || '',
                            ecclesiasticalRole: row['Cargo Eclesiástico'] || 'Miembro',
                            // Usar IDs mapeados desde los datos jerárquicos (para POST request)
                            fieldAssignment: hierarchicalIds.fieldAssignmentId,
                            zoneAssignment: hierarchicalIds.zoneAssignmentId,
                            districtAssignment: hierarchicalIds.districtAssignmentId,
                            churchAssignment: hierarchicalIds.churchAssignmentId
                        })

                        processedParticipants.push(newUser)
                        newUsersCreated++
                    } catch (createError) {
                        console.error(`Error al crear usuario en fila ${rowNumber}:`, createError)

                        // Proporcionar información más específica del error
                        let errorMessage = `Fila ${rowNumber}: No se pudo crear el usuario`
                        if (createError.message && createError.message.includes('Error del servidor:')) {
                            errorMessage += ` - ${createError.message}`
                        } else if (createError.response?.data?.error?.message) {
                            errorMessage += ` - ${createError.response.data.error.message}`
                        }

                        errors.push(errorMessage)

                        // Como fallback, crear un participante temporal (solo para el evento)
                        const tempParticipant: Participant = {
                            id: `temp_${Date.now()}_${i}`,
                            firstName: row['Nombre'],
                            lastName: row['Apellido'],
                            email: row['Email'],
                            phone: row['Teléfono'] || '',
                            ecclesiasticalRole: row['Cargo Eclesiástico'] || 'Miembro',
                            // Usar IDs mapeados desde los datos jerárquicos
                            fieldAssignmentId: hierarchicalIds.fieldAssignmentId,
                            zoneAssignmentId: hierarchicalIds.zoneAssignmentId,
                            districtAssignmentId: hierarchicalIds.districtAssignmentId,
                            churchAssignmentId: hierarchicalIds.churchAssignmentId
                        }

                        processedParticipants.push(tempParticipant)
                        newUsersCreated++
                    }
                }
            } catch (error) {
                console.error(`Error al procesar participante en fila ${rowNumber}:`, error)
                errors.push(`Fila ${rowNumber}: Error al procesar participante`)

                // Fallback a datos mockeados para esta fila específica
                const mockUser = mockMeetingParticipants.find(
                    participant => participant.email.toLowerCase() === row['Email'].toLowerCase()
                )

                if (mockUser && !selectedParticipants.some(p => p.id === mockUser.id)) {
                    processedParticipants.push(mockUser)
                    existingUsersAdded++
                }
            }
        }

        return {
            participants: processedParticipants,
            errors,
            stats: {
                newUsersCreated,
                existingUsersAdded,
                totalProcessed: processedParticipants.length
            }
        }
    }

    // Manejar subida de archivo para importar
    const handleFileChange = async (files: File[]) => {
        const file = files[0]
        setLoadingImport(true)

        const reader = new FileReader()

        reader.onload = async (evt) => {
            try {
                const bstr = evt.target?.result
                const wb = XLSX.read(bstr as string, { type: 'binary' })
                const wsname = wb.SheetNames[0]
                const ws = wb.Sheets[wsname]
                const data = XLSX.utils.sheet_to_json(ws)

                if (data.length === 0) {
                    toast.push(
                        <Notification title="Archivo vacío" type="warning">
                            El archivo Excel no contiene datos para importar.
                        </Notification>
                    )
                    return
                }

                // Validar y procesar participantes usando la API real
                const result = await validateAndProcessParticipants(data)

                if (result.errors.length > 0) {
                    // Mostrar errores pero continuar con los participantes válidos
                    console.warn('Errores durante la importación:', result.errors)

                    // Mostrar los primeros 3 errores en la notificación
                    const firstErrors = result.errors.slice(0, 3)
                    const remainingCount = result.errors.length - 3

                    toast.push(
                        <Notification title="Importación con errores" type="warning">
                            <div>
                                <p>Se encontraron {result.errors.length} errores:</p>
                                <ul className="mt-2 text-sm">
                                    {firstErrors.map((error, index) => (
                                        <li key={index} className="mb-1">• {error}</li>
                                    ))}
                                    {remainingCount > 0 && (
                                        <li className="mb-1">• ... y {remainingCount} errores más (ver consola)</li>
                                    )}
                                </ul>
                            </div>
                        </Notification>
                    )
                }

                if (result.participants.length > 0) {
                    // Agregar participantes válidos
                    setSelectedParticipants(prev => {
                        const existingIds = prev.map(p => p.id)
                        const newParticipants = result.participants.filter(p => !existingIds.includes(p.id))
                        return [...prev, ...newParticipants]
                    })

                    toast.push(
                        <Notification title="Importación exitosa" type="success">
                            {result.stats.totalProcessed} participantes procesados: 
                            {result.stats.existingUsersAdded} usuarios existentes añadidos, 
                            {result.stats.newUsersCreated} nuevos usuarios creados.
                        </Notification>
                    )
                } else {
                    toast.push(
                        <Notification title="Sin participantes válidos" type="warning">
                            No se pudieron procesar participantes válidos del archivo.
                        </Notification>
                    )
                }

                setisImportModalOpen(false)
            } catch (e) {
                console.error('Error al parsear el archivo Excel', e)
                toast.push(
                    <Notification title="Error de importación" type="danger">
                        No se pudo parsear el archivo. Verifique que sea un archivo Excel válido.
                    </Notification>
                )
            } finally {
                setLoadingImport(false)
            }
        }

        reader.readAsBinaryString(file)
    }

    // Manejar el envío del formulario
    const handleSubmit_old = async (values: ReturnType<typeof getInitialValues>) => {
        try {
            let dateString = values.date
            if (values.date instanceof Date) {
                dateString = values.date.toISOString().split('T')[0]
            }

            const eventToSave: Partial<Event> = {
                ...values,
                date: dateString,
                participantsInvited: selectedParticipants,
                invitingDepartment: values.invitingDepartment,
            }

            console.log('Guardando evento con participantes:', selectedParticipants.length)

            if (isEditMode && id) {
                const updatedEvent = await EventsService.updateEvent(id, eventToSave)

                if (updatedEvent) {
                    console.log('Evento actualizado:', updatedEvent)
                    console.log('Participantes guardados:', updatedEvent.participantsInvited?.length || 0)

                    toast.push(
                        <Notification title="Evento actualizado" type="success">
                            El evento ha sido actualizado correctamente con {selectedParticipants.length} participantes.
                        </Notification>,
                    )
                }
            } else {
                const newEvent = await EventsService.createEvent(eventToSave as Omit<Event, 'id'>)

                console.log('Nuevo evento creado:', newEvent)
                console.log('Participantes guardados:', newEvent.participantsInvited?.length || 0)

                toast.push(
                    <Notification title="Evento creado" type="success">
                        El evento ha sido creado correctamente con {selectedParticipants.length} participantes.
                    </Notification>,
                )
            }

            navigate('/events/list')
        } catch (error) {
            console.error('Error al guardar el evento:', error)

            toast.push(
                <Notification title="Error" type="danger">
                    Ocurrió un error al guardar el evento. Por favor, inténtelo de nuevo.
                </Notification>,
            )
        }
    }

    // Abrir el modal de añadir participantes
    const handleOpenModal = (e?: React.MouseEvent) => {
        // Prevenir el comportamiento predeterminado si se proporciona un evento
        if (e) {
            e.preventDefault()
            e.stopPropagation()
        }
        setIsModalOpen(true)
        setFilteredModalParticipants([]) // Iniciar vacío para evitar problemas de rendimiento
        setModalSelectedParticipants([])
        // Los participantes se cargarán cuando el usuario aplique filtros o busque
    }

    // Cerrar el modal de añadir participantes
    const handleCloseModal = () => {
        setIsModalOpen(false)
        resetModalFilters()
    }

    // Resetear los filtros del modal
    const resetModalFilters = () => {
        setSearchTerm('')
        setSelectedField(null)
        setSelectedZone(null)
        setSelectedDistrict(null)
        setSelectedChurch(null)
        setSelectedRole(null)
        setFilteredModalParticipants([])
        setModalSelectedParticipants([])
    }
    
    // Manejar cambio en el campo seleccionado
    const handleFieldChange = (fieldId: string | number | null) => {
        setSelectedField(fieldId)
        // Resetear zona, distrito y iglesia cuando cambia el campo
        setSelectedZone(null)
        setSelectedDistrict(null)
        setSelectedChurch(null)
    }
    
    // Manejar cambio en la zona seleccionada
    const handleZoneChange = (zoneId: string | number | null) => {
        setSelectedZone(zoneId)
        // Resetear el distrito y la iglesia seleccionada cuando cambia la zona
        setSelectedDistrict(null)
        setSelectedChurch(null)
    }
    
    // Manejar cambio en el distrito seleccionado
    const handleDistrictChange = (districtId: string | number | null) => {
        setSelectedDistrict(districtId)
        // Resetear la iglesia seleccionada cuando cambia el distrito
        setSelectedChurch(null)
    }

    // Añadir un participante al evento
    const handleAddParticipantToEvent = (participant: Participant) => {
        // Verificar si el participante ya está seleccionado
        if (!selectedParticipants.some(p => p.id === participant.id)) {
            setSelectedParticipants([...selectedParticipants, participant])
        }
    }

    // Eliminar un participante del evento
    const handleRemoveParticipantFromEvent = (participantId: string | number) => {
        setSelectedParticipants(selectedParticipants.filter(p => p.id !== participantId))
    }

    // Aplicar filtros en el modal usando la API real
    const handleApplyFiltersInModal = async () => {
        setLoadingParticipants(true)

        try {
            // Preparar filtros para la API
            const filters = {
                search: searchTerm || undefined,
                fieldAssignmentId: selectedField || undefined,
                zoneAssignmentId: selectedZone || undefined,
                districtAssignmentId: selectedDistrict || undefined,
                churchAssignmentId: selectedChurch || undefined,
                ecclesiasticalRole: typeof selectedRole === 'string' ? selectedRole : undefined
            }

            // Obtener participantes de la API
            const participants = await ParticipantsService.getUsers(filters)

            console.log('Participantes obtenidos de la API:', participants.length)
            setFilteredModalParticipants(participants)
        } catch (error) {
            console.error('Error al cargar participantes:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudieron cargar los participantes. Usando datos locales.
                </Notification>
            )

            // Fallback a datos mockeados en caso de error
            let filtered = [...mockMeetingParticipants]

            // Aplicar filtros localmente como fallback
            if (searchTerm) {
                const term = searchTerm.toLowerCase()
                filtered = filtered.filter(
                    participant =>
                        participant.firstName?.toLowerCase().includes(term) ||
                        participant.lastName?.toLowerCase().includes(term) ||
                        participant.email.toLowerCase().includes(term) ||
                        participant.ecclesiasticalRole?.toLowerCase().includes(term)
                )
            }

            if (selectedField) {
                filtered = filtered.filter(participant =>
                    participant.fieldAssignment?.documentId === selectedField ||
                    participant.fieldAssignmentId === selectedField
                )
            }

            if (selectedZone) {
                filtered = filtered.filter(participant =>
                    participant.zoneAssignment?.documentId === selectedZone ||
                    participant.zoneAssignmentId === selectedZone
                )
            }

            if (selectedDistrict) {
                filtered = filtered.filter(participant =>
                    participant.districtAssignment?.documentId === selectedDistrict ||
                    participant.districtAssignmentId === selectedDistrict
                )
            }

            if (selectedChurch) {
                filtered = filtered.filter(participant =>
                    participant.churchAssignment?.documentId === selectedChurch ||
                    participant.churchAssignmentId === selectedChurch
                )
            }

            if (selectedRole) {
                filtered = filtered.filter(participant => participant.ecclesiasticalRole === selectedRole)
            }

            setFilteredModalParticipants(filtered)
        } finally {
            setLoadingParticipants(false)
        }
    }

    // Manejar selección de participante en el modal
    const handleToggleParticipantSelection = (participantId: string) => {
        if (modalSelectedParticipants.includes(participantId)) {
            setModalSelectedParticipants(modalSelectedParticipants.filter(id => id !== participantId))
        } else {
            setModalSelectedParticipants([...modalSelectedParticipants, participantId])
        }
    }

    // Añadir participantes seleccionados desde el modal
    const handleAddSelectedFromModal = () => {
        const participantsToAdd = filteredModalParticipants.filter(
            participant => modalSelectedParticipants.includes(participant.id.toString())
        )

        // Añadir solo los que no están ya seleccionados
        const newParticipants = participantsToAdd.filter(
            participant => !selectedParticipants.some(p => p.id === participant.id)
        )

        setSelectedParticipants([...selectedParticipants, ...newParticipants])
        handleCloseModal()
    }

    // Reabrir un evento completado
    const handleReopenEvent = () => {
        if (isEditMode && id && eventData?.eventStatus === 'completada') {
            // En una implementación real, aquí se haría la llamada a la API
            console.log('Reabriendo evento:', id)

            // Actualizar el estado local
            setEventData({
                ...eventData,
                eventStatus: 'en-progreso'
            })

            toast.push(
                <Notification title="Evento reabierto" type="success">
                    El evento ha sido reabierto y ahora está en progreso.
                </Notification>
            )
        }
    }

    // Volver a la lista de eventos
    const handleBack = () => {
        navigate('/events/list')
    }

    if (loading) {
        return (
            <div className="flex justify-center items-center h-full">
                <p>Cargando información del evento...</p>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                        variant="plain"
                    >
                        Volver al Listado
                    </Button>
                    <h1 className="text-2xl font-bold">
                        {isEditMode ? 'Editar Evento' : 'Crear Nuevo Evento'}
                    </h1>
                </div>

                {isEditMode && eventData?.eventStatus === 'completada' && (
                    <Button
                        variant="solid"
                        color="yellow-500"
                        icon={<HiRefresh />}
                        onClick={handleReopenEvent}
                    >
                        Reabrir Evento
                    </Button>
                )}
            </div>

            <Formik
                initialValues={getInitialValues()}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
                innerRef={formikRef}
            >
                {({ values, touched, errors, setFieldValue }) => (
                    <Form>
                        <FormContainer>
                            <TypeWatcher eventTypes={eventTypes} />
                            <DateSyncWatcher />
                            {/* Sección de Información del Evento */}
                            <Card className="mb-6">
                                <div className="p-6">
                                    <h5 className="mb-4">Información del Evento</h5>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormItem
                                            label="Título"
                                            invalid={errors.title && touched.title}
                                            errorMessage={errors.title}
                                        >
                                            <Field
                                                type="text"
                                                name="title"
                                                placeholder="Título del evento"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="Asunto/Grupo"
                                            invalid={errors.subject && touched.subject}
                                            errorMessage={errors.subject}
                                        >
                                            <Field
                                                type="text"
                                                name="subject"
                                                placeholder="Asunto o grupo"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="Departamento que Invita"
                                            invalid={errors.invitingDepartment && touched.invitingDepartment}
                                            errorMessage={errors.invitingDepartment}
                                        >
                                            <Field name="invitingDepartment">
                                                {({ field, form }: any) => (
                                                    <Select
                                                        options={departmentOptions}
                                                        value={departmentOptions?.find(
                                                            (option) => option.value === field.value.id
                                                        )}
                                                        onChange={(option) => {
                                                            form.setFieldValue(field.name, option?.value)
                                                        }}
                                                        placeholder="Seleccionar departamento"
                                                    />
                                                )}
                                            </Field>
                                        </FormItem>

                                        <FormItem
                                            label="Tema"
                                            invalid={errors.topic && touched.topic}
                                            errorMessage={errors.topic}
                                        >
                                            <Field
                                                type="text"
                                                name="topic"
                                                placeholder="Tema específico"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label="Lugar"
                                            invalid={errors.location && touched.location}
                                            errorMessage={errors.location}
                                        >
                                            <Field
                                                type="text"
                                                name="location"
                                                placeholder="Ubicación del evento"
                                                component={Input}
                                            />
                                        </FormItem>

                                        <FormItem label="Tipo de Evento">
                                            <Field name="typeId">
                                                {({ field, form }: any) => (
                                                    <Select
                                                        options={eventTypes.map((t) => ({ value: t.id, label: t.name }))}
                                                        value={eventTypes
                                                            .map((t) => ({ value: t.id, label: t.name }))
                                                            .find((opt) => opt.value === field.value)}
                                                        onChange={(option) => {
                                                            form.setFieldValue(field.name, option?.value)
                                                        }}
                                                        placeholder="Seleccionar tipo"
                                                    />
                                                )}
                                            </Field>
                                        </FormItem>

                                        {/* Toggle para eventos de múltiples días */}
                                        <div className="mb-4">
                                            <FormItem label="Evento de múltiples días">
                                                <Field name="isMultiDay">
                                                    {({ field, form }: any) => (
                                                        <Switcher
                                                            checked={field.value}
                                                            onChange={(checked) => {
                                                                form.setFieldValue(field.name, checked)
                                                                // Si se desactiva, limpiar la fecha de fin
                                                                if (!checked) {
                                                                    form.setFieldValue('endDate', '')
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Primera fila: Fecha de inicio y Hora de inicio */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FormItem
                                                label="Fecha de inicio"
                                                invalid={errors.date && touched.date}
                                                errorMessage={errors.date}
                                            >
                                                <Field name="date">
                                                    {({ field, form }: any) => (
                                                        <DatePicker
                                                            placeholder="Seleccionar fecha de inicio"
                                                            value={field.value ? new Date(field.value + 'T00:00:00') : null}
                                                            onChange={(date) => {
                                                                if (date) {
                                                                    const formattedDate = date.toISOString().split('T')[0]
                                                                    form.setFieldValue(field.name, formattedDate)
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>

                                            <FormItem
                                                label="Hora de inicio"
                                                invalid={errors.startTime && touched.startTime}
                                                errorMessage={errors.startTime}
                                            >
                                                <Field name="startTime">
                                                    {({ field, form }: any) => (
                                                        <TimeInput
                                                            placeholder="HH:MM"
                                                            format="12"
                                                            amPmPlaceholder="AM/PM"
                                                            value={field.value}
                                                            onChange={(time) => {
                                                                form.setFieldValue(field.name, time)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Segunda fila: Fecha de fin y Hora de fin (condicional) */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {values.isMultiDay && (
                                                <FormItem
                                                    label="Fecha de fin"
                                                    invalid={errors.endDate && touched.endDate}
                                                    errorMessage={errors.endDate}
                                                >
                                                    <Field name="endDate">
                                                        {({ field, form }: any) => (
                                                            <DatePicker
                                                                placeholder="Seleccionar fecha de fin"
                                                                value={field.value ? new Date(field.value + 'T00:00:00') : null}
                                                                onChange={(date) => {
                                                                    if (date) {
                                                                        const formattedDate = date.toISOString().split('T')[0]
                                                                        form.setFieldValue(field.name, formattedDate)
                                                                    }
                                                                }}
                                                            />
                                                        )}
                                                    </Field>
                                                </FormItem>
                                            )}

                                            <FormItem
                                                label="Hora de fin"
                                                invalid={errors.endTime && touched.endTime}
                                                errorMessage={errors.endTime}
                                            >
                                                <Field name="endTime">
                                                    {({ field, form }: any) => (
                                                        <TimeInput
                                                            placeholder="HH:MM"
                                                            format="12"
                                                            amPmPlaceholder="AM/PM"
                                                            value={field.value}
                                                            onChange={(time) => {
                                                                form.setFieldValue(field.name, time)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Segunda fila: Estado y Registro Automático */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FormItem
                                                label="Estado"
                                                invalid={errors.eventStatus && touched.eventStatus}
                                                errorMessage={errors.eventStatus}
                                            >
                                                <Field name="eventStatus">
                                                    {({ field, form }: any) => (
                                                        <Select
                                                            options={statusOptions}
                                                            value={statusOptions.find(
                                                                (option) => option.value === field.value
                                                            )}
                                                            onChange={(option) => {
                                                                form.setFieldValue(field.name, option?.value)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>

                                            <FormItem label="Registro Automático">
                                                <Field name="autoRegistration">
                                                    {({ field, form }: any) => (
                                                        <Switcher
                                                            checked={field.value}
                                                            onChange={(checked) => {
                                                                form.setFieldValue(field.name, checked)
                                                                // Marcar que el usuario ha modificado este campo manualmente
                                                                form.setFieldValue('_userModifiedAutoRegistration', true)
                                                            }}
                                                        />
                                                    )}
                                                </Field>
                                            </FormItem>
                                        </div>

                                        {/* Tercera fila: Modo de asistencia (solo visible cuando hasMultipleSessions es false) */}
                                        {!values.hasMultipleSessions && (
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <FormItem label="Modo de asistencia">
                                                    <Field name="attendanceMode">
                                                        {({ field, form }: any) => (
                                                            <Radio.Group
                                                                value={field.value}
                                                                onChange={(val) => {
                                                                    form.setFieldValue(field.name, val)
                                                                    // Actualizar la sesión por defecto con el nuevo modo
                                                                    const updatedSessions = values.sessions.map((session: any) => {
                                                                        if (session.isDefault) {
                                                                            return {
                                                                                ...session,
                                                                                attendanceMode: val === 'kiosco_rapido' ? 'kiosk' : 'normal'
                                                                            }
                                                                        }
                                                                        return session
                                                                    })
                                                                    form.setFieldValue('sessions', updatedSessions)
                                                                }}
                                                                radioGutter={2}
                                                            >
                                                                <Radio value="manual">Manual</Radio>
                                                                <Radio value="kiosco_rapido">Kiosco Rápido</Radio>
                                                            </Radio.Group>
                                                        )}
                                                    </Field>
                                                </FormItem>
                                                <div></div> {/* Espacio vacío para mantener el grid */}
                                            </div>
                                        )}

                                        <FormItem
                                            label="Descripción"
                                            className="md:col-span-2"
                                            invalid={errors.description && touched.description}
                                            errorMessage={errors.description}
                                        >
                                            <Field
                                                type="text"
                                                name="description"
                                                placeholder="Descripción del evento"
                                                component={Input}
                                                textArea
                                                rows={4}
                                            />
                                        </FormItem>
                                    </div>
                                </div>
                            </Card>

                            {/* Sección de Sesiones de Asistencia - Movida por encima de participantes */}
                            <Card className="mb-6">
                                <div className="p-6">
                                    <div className="flex justify-between items-center mb-4">
                                        <h5>Sesiones de Asistencia</h5>
                                        <FormItem label="Múltiples sesiones de asistencia">
                                            <Field name="hasMultipleSessions">
                                                {({ field, form }: any) => (
                                                    <Switcher
                                                        checked={field.value}
                                                        onChange={(checked) => {
                                                            if (checked) {
                                                                // Activar modo múltiples sesiones
                                                                form.setFieldValue(field.name, checked)
                                                                
                                                                // Crear automáticamente una nueva sesión si solo hay una
                                                                if (values.sessions.length < 2) {
                                                                    // Determinar el método de asistencia por defecto según el tipo de evento
                                                                    const defaultAttendanceMethod = values.eventType?.attendanceMethodDefault || 'manual'
                                                                    const attendanceMode = defaultAttendanceMethod === 'kiosco_rapido' ? 'kiosk' : 'normal'
                                                                    
                                                                    const newSession = {
                                                                        id: `session-${Date.now()}`,
                                                                        date: values.date || new Date().toISOString().split('T')[0],
                                                                        comment: 'Sesión adicional',
                                                                        attendanceMode: attendanceMode as const,
                                                                        isDefault: false,
                                                                        status: 'pendiente', // Estado por defecto para nuevas sesiones
                                                                        attendanceRecords: [],
                                                                    }
                                                                    form.setFieldValue('sessions', [...values.sessions, newSession])
                                                                }
                                                            } else {
                                                                // Desactivar modo múltiples sesiones
                                                                if (values.sessions.length > 1) {
                                                                    // Mostrar modal de confirmación si hay múltiples sesiones
                                                                    setPendingToggleValue(checked)
                                                                    setShowSessionDeleteConfirm(true)
                                                                } else {
                                                                    // Si solo hay una sesión, proceder directamente
                                                                    form.setFieldValue(field.name, checked)
                                                                    const defaultSession = values.sessions.find((s: any) => s.isDefault) || values.sessions[0]
                                                                    if (defaultSession) {
                                                                        const updatedDefaultSession = {
                                                                            ...defaultSession,
                                                                            attendanceMode: values.attendanceMode === 'kiosco_rapido' ? 'kiosk' : 'normal',
                                                                            isDefault: true
                                                                        }
                                                                        form.setFieldValue('sessions', [updatedDefaultSession])
                                                                    }
                                                                }
                                                            }
                                                        }}
                                                    />
                                                )}
                                            </Field>
                                        </FormItem>
                                    </div>

                                    {/* Mensaje de validación para múltiples sesiones */}
                                    {values.hasMultipleSessions && values.sessions.length < 2 && (
                                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                                            <div className="flex items-center text-red-600">
                                                <HiInformationCircle className="mr-2" />
                                                <span className="font-medium">
                                                    Para eventos con múltiples sesiones debe haber al menos 2 sesiones.
                                                </span>
                                            </div>
                                            <div className="text-sm text-red-500 mt-1">
                                                Desactive el toggle &quot;Múltiples sesiones de asistencia&quot; o agregue más sesiones.
                                            </div>
                                        </div>
                                    )}

                                    {/* Mostrar tabla de sesiones solo si hasMultipleSessions es true */}
                                    {values.hasMultipleSessions ? (
                                        <SessionTable
                                            sessions={values.sessions}
                                            onUpdateSession={(idx, session) => {
                                                const updated = [...values.sessions]
                                                updated[idx] = session
                                                setFieldValue('sessions', updated)
                                            }}
                                            onAddSession={() => {
                                                const newSession = {
                                                    id: Date.now().toString(),
                                                    date: values.date || new Date().toISOString().split('T')[0],
                                                    comment: '',
                                                    attendanceMode: 'normal' as const,
                                                    isDefault: false,
                                                    status: 'pendiente', // Estado por defecto para nuevas sesiones
                                                    attendanceRecords: [],
                                                }
                                                setFieldValue('sessions', [...values.sessions, newSession])
                                            }}
                                            onDeleteSession={(idx) => {
                                                const updated = values.sessions.filter((_, i) => i !== idx)
                                                setFieldValue('sessions', updated)
                                            }}
                                            eventDates={{ start: values.date, end: values.endTime }}
                                        />
                                    ) : (
                                        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                            <div className="flex items-center text-gray-600">
                                                <HiInformationCircle className="mr-2" />
                                                <span>
                                                    Modo simple activado: Se creará automáticamente una sesión por defecto
                                                    con la configuración del evento.
                                                </span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </Card>

                            {/* Sección de Participantes */}
                            {values && (
                            <Card className="mb-6 border-2 border-blue-500">
                                <div className="p-6">
                                    <div className="flex flex-wrap justify-between items-center mb-4 gap-2">
                                        <h5 className="text-lg font-bold">Participantes</h5>
                                        <div className="flex gap-2">
                                            <Button
                                                variant="solid"
                                                size="sm"
                                                icon={<HiOutlineDocumentDownload />}
                                                type="button"
                                                onClick={(e) => {
                                                    e.preventDefault()
                                                    e.stopPropagation()
                                                    setisImportModalOpen(true)
                                                }}
                                            >
                                                Importar desde Excel
                                            </Button>
                                            <Button
                                                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4"
                                                variant="solid"
                                                size="sm"
                                                icon={<HiUserAdd />}
                                                type="button"
                                                onClick={handleOpenModal}
                                            >
                                                Añadir Participantes
                                            </Button>
                                        </div>
                                    </div>

                                    {selectedParticipants.length > 0 ? (
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            {selectedParticipants.map((participant) => (
                                                <div
                                                    key={participant.id}
                                                    className="flex items-center p-3 border rounded-lg"
                                                >
                                                    <Avatar
                                                        src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                                        size={40}
                                                        className="mr-3"
                                                    />
                                                    <div className="flex-grow">
                                                        <div className="font-medium">
                                                            {participant.firstName} {participant.lastName}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {participant.ecclesiasticalRole || participant.email}
                                                        </div>
                                                    </div>
                                                    <Button
                                                        variant="plain"
                                                        size="sm"
                                                        icon={<HiXCircle />}
                                                        onClick={() => handleRemoveParticipantFromEvent(participant.id)}
                                                        type="button"
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 text-gray-500">
                                            Aún no se han añadido participantes
                                        </div>
                                    )}

                                    {/* Opción de enviar notificaciones - Movida aquí */}
                                    <div className="mt-6 pt-4 border-t">
                                        <FormItem>
                                            <Field name="sendNotifications">
                                                {({ field, form }: any) => (
                                                    <Checkbox
                                                        checked={field.value}
                                                        onChange={(checked) => {
                                                            form.setFieldValue(field.name, checked)
                                                        }}
                                                    >
                                                        Enviar notificación a los participantes
                                                    </Checkbox>
                                                )}
                                            </Field>
                                        </FormItem>
                                    </div>
                                </div>
                            </Card>
                            )}

                            {/* Botones de Acción */}
                            <div className="flex justify-end">
                                <Button
                                    variant="plain"
                                    className="mr-2"
                                    onClick={handleBack}
                                    type="button"
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    variant="solid"
                                    type="submit"
                                    icon={<HiSave />}
                                >
                                    Guardar Evento
                                </Button>
                            </div>
                        </FormContainer>
                    </Form>
                    )}
            </Formik>

            {/* Modal de Añadir Participantes */}
            <Dialog
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onRequestClose={handleCloseModal}
                width={800}
                contentClassName="p-0 flex flex-col"
                className="max-h-[90vh]" // Added Tailwind class for max height on the Dialog itself
            >
                <h4 className="text-lg font-semibold mb-4 px-6 pt-6 sticky top-0 bg-white z-10 border-b pb-4">
                    Añadir Participantes
                </h4>
                <div className="px-6 pb-4 overflow-y-auto flex-grow">
                    {/* Búsqueda Individual */}
                    <div className="mb-6">
                        <h6 className="mb-2">Búsqueda Individual</h6>
                        <div className="flex items-center gap-2">
                            <Input
                                prefix={<HiSearch className="text-lg" />}
                                value={searchTerm}
                                onChange={e => setSearchTerm(e.target.value)}
                                placeholder="Buscar por nombre, email o cargo..."
                                className="flex-grow"
                            />
                            <Button
                                variant="solid"
                                size="sm"
                                onClick={handleApplyFiltersInModal}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                                Buscar
                            </Button>
                        </div>
                    </div>

                    {/* Filtros Jerárquicos */}
                    <div className="mb-6">
                        <div className="flex justify-between items-center mb-2">
                            <h6 className="mb-0">Filtrar por Estructura Eclesiástica</h6>
                            <Button
                                variant="link"
                                size="sm"
                                className="text-blue-600 hover:underline p-0 h-auto"
                                onClick={resetModalFilters} // Llama a la función existente para limpiar filtros
                            >
                                Limpiar todos los filtros
                            </Button>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4 items-end">
                            {/* Campo Eclesiástico */}
                            <div>
                                <label className="form-label mb-2">Campo Eclesiástico</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todos los Campos' },
                                        ...mockHierarchicalData.fields.map(field => ({
                                            value: field.id,
                                            label: field.name
                                        }))
                                    ]}
                                    value={selectedField === null ? 
                                        { value: null, label: 'Todos los Campos' } : 
                                        mockHierarchicalData.fields
                                            .filter(field => field.id === selectedField)
                                            .map(field => ({ value: field.id, label: field.name }))[0]
                                    }
                                    onChange={option => handleFieldChange(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Campo"
                                />
                            </div>
                            {/* Zona Pastoral */}
                            <div>
                                <label className="form-label mb-2">Zona Pastoral</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todas las Zonas' },
                                        ...mockHierarchicalData.zones
                                            .filter(zone => !selectedField || zone.fieldId === selectedField)
                                            .map(zone => ({
                                                value: zone.id,
                                                label: zone.name
                                            }))
                                    ]}
                                    value={selectedZone === null ? 
                                        { value: null, label: 'Todas las Zonas' } : 
                                        mockHierarchicalData.zones
                                            .filter(zone => zone.id === selectedZone)
                                            .map(zone => ({ value: zone.id, label: zone.name }))[0]
                                    }
                                    onChange={option => handleZoneChange(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Zona"
                                    isDisabled={selectedField === null}
                                />
                            </div>
                            {/* Distrito Pastoral */}
                            <div>
                                <label className="form-label mb-2">Distrito Pastoral</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todos los Distritos' },
                                        ...mockHierarchicalData.districts
                                            .filter(district => !selectedZone || district.zoneId === selectedZone)
                                            .map(district => ({
                                                value: district.id,
                                                label: district.name
                                            }))
                                    ]}
                                    value={selectedDistrict === null ? 
                                        { value: null, label: 'Todos los Distritos' } : 
                                        mockHierarchicalData.districts
                                            .filter(district => district.id === selectedDistrict)
                                            .map(district => ({ value: district.id, label: district.name }))[0]
                                    }
                                    onChange={option => handleDistrictChange(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Distrito"
                                    isDisabled={selectedZone === null}
                                />
                            </div>
                            {/* Iglesia Local */}
                            <div>
                                <label className="form-label mb-2">Iglesia Local</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todas las Iglesias' },
                                        ...mockHierarchicalData.churches
                                            .filter(church => !selectedDistrict || church.districtId === selectedDistrict)
                                            .map(church => ({
                                                value: church.id,
                                                label: church.name
                                            }))
                                    ]}
                                    value={selectedChurch === null ? 
                                        { value: null, label: 'Todas las Iglesias' } : 
                                        mockHierarchicalData.churches
                                            .filter(church => church.id === selectedChurch)
                                            .map(church => ({ value: church.id, label: church.name }))[0]
                                    }
                                    onChange={option => setSelectedChurch(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Iglesia"
                                    isDisabled={selectedDistrict === null}
                                />
                            </div>
                            {/* Cargo/Ministerio */}
                            <div>
                                <label className="form-label mb-2">Cargo/Ministerio</label>
                                <Select
                                    options={[
                                        { value: null, label: 'Todos los Cargos' },
                                        ...mockHierarchicalData.ecclesiasticalRoles.map(role => ({
                                            value: role,
                                            label: role
                                        }))
                                    ]}
                                    value={selectedRole === null ? 
                                        { value: null, label: 'Todos los Cargos' } : 
                                        (selectedRole ? { value: selectedRole, label: selectedRole } : null)
                                    }
                                    onChange={option => setSelectedRole(option?.value !== undefined ? option.value : null)}
                                    placeholder="Seleccionar Cargo"
                                />
                            </div>
                            {/* Botón Aplicar Filtros y Buscar Miembros */}
                            <div className="w-full">
                                <Button
                                    variant="solid"
                                    size="sm"
                                    onClick={handleApplyFiltersInModal}
                                    className="bg-blue-600 hover:bg-blue-700 text-white w-full"
                                >
                                    Aplicar Filtros y Buscar Miembros
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Resultados de la Búsqueda */}
                    <div>
                        <h6 className="mb-2">
                            Resultados ({filteredModalParticipants.length})
                            {loadingParticipants && <span className="ml-2 text-sm text-gray-500">Cargando...</span>}
                        </h6>
                        <div className="border rounded-md p-2">
                            {loadingParticipants ? (
                                <div className="flex justify-center items-center py-8">
                                    <div className="text-gray-500">Cargando participantes...</div>
                                </div>
                            ) : filteredModalParticipants.length > 0 ? (
                                filteredModalParticipants.map(participant => {
                                    const isSelected = modalSelectedParticipants.includes(participant.id.toString())
                                    const isAlreadyAdded = selectedParticipants.some(p => p.id === participant.id)

                                    return (
                                        <div
                                            key={participant.id}
                                            className={`flex items-center p-3 border-b ${isSelected ? 'bg-gray-100' : ''} ${isAlreadyAdded ? 'opacity-50' : ''}`}
                                        >
                                            <Checkbox
                                                className="mr-3"
                                                checked={isSelected}
                                                disabled={isAlreadyAdded}
                                                onChange={() => handleToggleParticipantSelection(participant.id.toString())}
                                            />
                                            <Avatar
                                                src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                                size={40}
                                                className="mr-3"
                                            />
                                            <div className="flex-grow">
                                                <div className="font-medium">
                                                    {participant.firstName} {participant.lastName}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {participant.ecclesiasticalRole || participant.email}
                                                </div>
                                            </div>
                                            {isAlreadyAdded && (
                                                <span className="text-sm text-gray-500">Ya añadido</span>
                                            )}
                                        </div>
                                    )
                                })
                            ) : (
                                <div className="text-center py-4 text-gray-500">
                                    No se encontraron participantes con los filtros aplicados
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                <div className="flex justify-end px-6 py-4 border-t bg-gray-50 sticky bottom-0 z-10">
                    <Button
                        variant="plain"
                        className="mr-2"
                        onClick={handleCloseModal}
                    >
                        Cancelar
                    </Button>
                    <Button
                        variant="solid"
                        onClick={handleAddSelectedFromModal}
                        disabled={modalSelectedParticipants.length === 0}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                        Añadir Seleccionados ({modalSelectedParticipants.length})
                    </Button>
                </div>
            </Dialog>

            {/* Modal de Importar Participantes - Movido fuera del formulario para evitar envío automático */}
            <Dialog
                isOpen={isImportModalOpen}
                onClose={() => setisImportModalOpen(false)}
                onRequestClose={() => setisImportModalOpen(false)}
                width={700}
                title="Importar Participantes desde Excel"
            >
                <form onSubmit={(e) => e.preventDefault()}>
                    <div className="p-6">
                    <div className="mb-6">
                        <h6 className="text-lg font-semibold mb-3">Instrucciones de Importación</h6>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                            <h6 className="font-medium text-blue-800 mb-2 block">Formato del Archivo Excel:</h6>
                            <ul className="text-sm text-blue-700 space-y-1">
                                <li>• <strong>Nombre:</strong> Nombre del participante (obligatorio)</li>
                                <li>• <strong>Apellido:</strong> Apellido del participante (obligatorio)</li>
                                <li>• <strong>Email:</strong> Correo electrónico (obligatorio, usado para identificar usuarios existentes)</li>
                                <li>• <strong>Teléfono:</strong> Número de teléfono (opcional, usado para identificar usuarios existentes)</li>
                                <li>• <strong>Cargo Eclesiástico:</strong> Rol en la iglesia (opcional)</li>
                                <li>• <strong>Campo, Zona, Distrito, Iglesia:</strong> Estructura eclesiástica (opcional)</li>
                            </ul>
                        </div>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                            <h6 className="font-medium text-green-800 mb-2 block">Lógica de Importación:</h6>
                            <ul className="text-sm text-green-700 space-y-1">
                                <li>• Si el usuario existe (por email o teléfono): se añade al evento</li>
                                <li>• Si el usuario no existe: se crea un nuevo usuario y se añade al evento</li>
                                <li>• Los usuarios duplicados en el evento se omiten automáticamente</li>
                            </ul>
                        </div>
                        <div className="flex gap-3">
                            <Button
                                variant="solid"
                                size="sm"
                                icon={<HiOutlineDocumentDownload />}
                                onClick={handleDownloadTemplate}
                                className="bg-green-600 hover:bg-green-700 text-white"
                                type="button"
                            >
                                Descargar Plantilla Excel
                            </Button>
                            <Button
                                variant="plain"
                                size="sm"
                                icon={<HiRefresh />}
                                onClick={() => setisImportModalOpen(false)}
                                type="button"
                            >
                                Cancelar
                            </Button>
                        </div>
                    </div>
                    
                    <div className="border-t pt-4">
                        <h6 className="font-semibold mb-3">Subir Archivo Excel</h6>
                        <p className="mb-4 text-gray-600">Seleccione un archivo .xlsx o .xls para importar la lista de participantes.</p>
                        {/* Envolver el Upload en un div para evitar que interfiera con el formulario principal */}
                        <div 
                            onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                            }}
                            onSubmit={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                            }}
                        >
                            <Upload
                                draggable
                                accept=".xlsx, .xls"
                                onChange={(files) => {
                                    handleFileChange(files)
                                }}
                                disabled={loadingImport}
                                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
                            >
                                <div className="text-gray-500">
                                    {loadingImport ? (
                                        <>
                                            <div className="mx-auto text-3xl mb-2 animate-spin">⏳</div>
                                            <p>Procesando archivo...</p>
                                            <p className="text-sm mt-1">Validando y creando usuarios en el sistema</p>
                                        </>
                                    ) : (
                                        <>
                                            <HiOutlineDocumentDownload className="mx-auto text-3xl mb-2" />
                                            <p>Arrastra tu archivo Excel aquí o haz clic para seleccionar</p>
                                            <p className="text-sm mt-1">Formatos soportados: .xlsx, .xls</p>
                                        </>
                                    )}
                                </div>
                            </Upload>
                        </div>
                    </div>
                    </div>
                </form>
            </Dialog>

            {/* Modal de confirmación para eliminación de sesiones */}
            <ConfirmDialog
                isOpen={showSessionDeleteConfirm}
                type="warning"
                title="Confirmar eliminación de sesiones"
                confirmText="Eliminar sesiones"
                cancelText="Cancelar"
                onConfirm={() => {
                    // Proceder con la desactivación del toggle y eliminación de sesiones
                    const { setFieldValue } = formikRef.current || {}
                    if (setFieldValue) {
                        setFieldValue('hasMultipleSessions', pendingToggleValue)
                        
                        // Mantener solo la sesión principal (primera o la marcada como default)
                        const defaultSession = formikRef.current?.values.sessions.find((s: any) => s.isDefault) || formikRef.current?.values.sessions[0]
                        if (defaultSession) {
                            const updatedDefaultSession = {
                                ...defaultSession,
                                attendanceMode: formikRef.current?.values.attendanceMode === 'kiosco_rapido' ? 'kiosk' : 'normal',
                                isDefault: true
                            }
                            setFieldValue('sessions', [updatedDefaultSession])
                        }
                    }
                    setShowSessionDeleteConfirm(false)
                }}
                onCancel={() => {
                    // Cancelar la operación, mantener el toggle en true
                    setShowSessionDeleteConfirm(false)
                }}
                onClose={() => {
                    setShowSessionDeleteConfirm(false)
                }}
            >
                <p>
                    Al desactivar las múltiples sesiones de asistencia, se eliminarán todas las sesiones adicionales 
                    y solo se mantendrá la sesión principal.
                </p>
                <p className="mt-2 font-medium text-amber-600">
                    Esta acción no se puede deshacer. ¿Está seguro de que desea continuar?
                </p>
            </ConfirmDialog>
        </div>
    )
}

export default EventFormView

// ...
    // Formatear fecha (YYYY-MM-DD -> DD/MM/YYYY)
    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-')
        return `${day}/${month}/${year}`
    }

    // Formatear hora de 24h a 12h con AM/PM
    const formatTime = (timeString: string) => {
        if (!timeString) return 'No especificado'
        
        const [hours, minutes] = timeString.split(':')
        const hour = parseInt(hours, 10)
        const ampm = hour >= 12 ? 'PM' : 'AM'
        const hour12 = hour % 12 || 12
        
        return `${hour12}:${minutes} ${ampm}`
    }
