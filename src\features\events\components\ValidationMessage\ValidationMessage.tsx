import { Alert } from '@/shared/components/ui'
import type { ValidationResult } from '../../utils/eventValidations'

interface ValidationMessageProps {
    validation: ValidationResult
    className?: string
    showIcon?: boolean
}

/**
 * Componente para mostrar mensajes de validación de manera consistente
 */
const ValidationMessage = ({ 
    validation, 
    className = '', 
    showIcon = true 
}: ValidationMessageProps) => {
    if (!validation.message) {
        return null
    }

    // Mapear tipos de validación a tipos de Alert
    const alertType = validation.type === 'error' ? 'danger' : 
                     validation.type === 'warning' ? 'warning' : 
                     validation.type === 'info' ? 'info' : 'info'

    return (
        <Alert 
            type={alertType}
            showIcon={showIcon}
            className={className}
        >
            {validation.message}
        </Alert>
    )
}

export default ValidationMessage
