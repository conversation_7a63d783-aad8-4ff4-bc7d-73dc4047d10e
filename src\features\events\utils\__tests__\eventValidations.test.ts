import { describe, it, expect } from 'vitest'
import {
    validateEventCompletion,
    validateSessionStatusChange,
    validateAttendanceRegistration,
    validateKioskSessionCompletion,
    getEventProgressInfo,
    validateSessionDeletion
} from '../eventValidations'
import type { Event, AttendanceSessionStatus } from '../../types'

const makeSession = (id: string, status: AttendanceSessionStatus) => ({
    id,
    date: '2023-01-01',
    attendanceMode: 'normal',
    isDefault: id === 's1',
    status,
    attendanceRecords: []
})

const createEvent = (statuses: AttendanceSessionStatus[]): Event => ({
    id: 1,
    title: 'E',
    date: '2023-01-01',
    startTime: '10:00',
    location: 'X',
    eventStatus: 'programada',
    participantsInvited: [],
    sessions: statuses.map((s, i) => makeSession(`s${i+1}`, s))
})

describe('eventValidations', () => {
    it('validateEventCompletion detecta pendientes', () => {
        const result = validateEventCompletion(createEvent(['pendiente']))
        expect(result.isValid).toBe(false)
    })

    it('validateSessionStatusChange reglas basicas', () => {
        const res1 = validateSessionStatusChange('completada', 'pendiente', true)
        expect(res1.isValid).toBe(false)
        const res2 = validateSessionStatusChange('completada', 'en_progreso')
        expect(res2.type).toBe('warning')
    })

    it('validateAttendanceRegistration', () => {
        expect(validateAttendanceRegistration('completada').isValid).toBe(false)
        expect(validateAttendanceRegistration('pendiente').type).toBe('info')
    })

    it('validateKioskSessionCompletion', () => {
        const res = validateKioskSessionCompletion('pendiente', 'kiosk', 10, 5)
        expect(res.type).toBe('warning')
    })

    it('getEventProgressInfo', () => {
        const info = getEventProgressInfo(createEvent(['completada', 'pendiente']))
        expect(info.message).toContain('sesiones completadas')
    })

    it('validateSessionDeletion', () => {
        const res1 = validateSessionDeletion('completada', true, false)
        expect(res1.isValid).toBe(false)
        const res2 = validateSessionDeletion('pendiente', true, false)
        expect(res2.type).toBe('warning')
    })
})
