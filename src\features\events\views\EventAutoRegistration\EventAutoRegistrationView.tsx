import { useState, useEffect, useRef, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Card from '@/shared/components/ui/Card'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Select from '@/shared/components/ui/Select'
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast'
import Loading from '@/shared/components/shared/Loading'
import Upload from '@/shared/components/ui/Upload'
import Dialog from '@/shared/components/ui/Dialog'
import MediaService from '@/shared/services/MediaService'
import Webcam from 'react-webcam'
import * as faceapi from 'face-api.js'
import Spinner from '@/shared/components/ui/Spinner'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import type { Event, HierarchicalFilterData } from '../../types'
import EventsService from '../../services/EventsService'
import { HiCheckCircle } from 'react-icons/hi'
import { useAuth } from '@/features/auth/hooks'

interface AutoRegValues {
    firstName: string
    lastName: string
    email: string
    phone: string
    fieldAssignmentId: string | number | null
    zoneAssignmentId: string | number | null
    districtAssignmentId: string | number | null
    churchAssignmentId: string | number | null
    ecclesiasticalRole: string | number | null
    photo?: File | null
}

const validationSchema = Yup.object({
    firstName: Yup.string().required('Este campo es obligatorio'),
    lastName: Yup.string().required('Este campo es obligatorio'),
    email: Yup.string().email('Email inválido').required('Este campo es obligatorio'),
    phone: Yup.string().required('Este campo es obligatorio'),
    fieldAssignmentId: Yup.mixed().required('Este campo es obligatorio'),
    zoneAssignmentId: Yup.mixed().required('Este campo es obligatorio'),
    districtAssignmentId: Yup.mixed().required('Este campo es obligatorio'),
    churchAssignmentId: Yup.mixed().required('Este campo es obligatorio'),
    ecclesiasticalRole: Yup.mixed().required('Este campo es obligatorio'),
})

const EventAutoRegistrationView = () => {
    const { eventId } = useParams<{ eventId: string }>()
    const navigate = useNavigate()
    const { authenticated } = useAuth()
    const canCloseWindow = typeof window !== 'undefined' && !!window.opener
    const [event, setEvent] = useState<Event | null>(null)
    const [hierarchy, setHierarchy] = useState<HierarchicalFilterData | null>(null)
    const [loading, setLoading] = useState(true)
    const [success, setSuccess] = useState(false)
    const [zones, setZones] = useState<Array<{ id: string | number; name: string; fieldId: string | number }>>([])
    const [districts, setDistricts] = useState<Array<{ id: string | number; name: string; zoneId: string | number }>>([])
    const [churches, setChurches] = useState<Array<{ id: string | number; name: string; districtId: string | number }>>([])
    const [photoPreview, setPhotoPreview] = useState<string | null>(null)
    const [cameraOpen, setCameraOpen] = useState(false)
    const [faceValid, setFaceValid] = useState<boolean | null>(null)
    const [validating, setValidating] = useState(false)
    const webcamRef = useRef<Webcam>(null)

    // Cargar modelos de detección facial
    useEffect(() => {
        faceapi.nets.tinyFaceDetector.loadFromUri(
            'https://justadudewhohacks.github.io/face-api.js/models',
        )
    }, [])

    const handleFileChange = async (
        files: File[],
        setFieldValue: (field: string, value: unknown) => void,
    ) => {
        if (photoPreview) {
            MediaService.revokeLocalPreview(photoPreview)
        }
        setPhotoPreview(null)
        
        if (files.length > 0) {
            const file = files[0]
            const localUrl = MediaService.createLocalPreview(file)
            
            // Validar rostro en la imagen
            setValidating(true)
            try {
                const img = new Image()
                img.onload = async () => {
                    const detections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions())
                    setFaceValid(detections.length > 0)
                }
                img.src = localUrl
            } catch (error) {
                console.error('Error validating face:', error)
                setFaceValid(false)
            } finally {
                setValidating(false)
            }
            
            setTimeout(() => {
                setPhotoPreview(localUrl)
            }, 0)
            setFieldValue('photo', file)
        }
    }
    const removePhoto = (setFieldValue: (field: string, value: unknown) => void) => {
        if (photoPreview) {
            MediaService.revokeLocalPreview(photoPreview)
        }
        setPhotoPreview(null)
        setFaceValid(null)
        setFieldValue('photo', null)
    }

    const checkFace = useCallback(async (imageSrc: string) => {
        try {
            const img = await faceapi.fetchImage(imageSrc)
            const detections = await faceapi.detectAllFaces(
                img,
                new faceapi.TinyFaceDetectorOptions(),
            )
            setFaceValid(detections.length > 0)
        } catch (error) {
            console.error('Error validating face:', error)
            setFaceValid(null)
        }
    }, [])

    const capturePhoto = async (setFieldValue: (field: string, value: unknown) => void) => {
        const imageSrc = webcamRef.current?.getScreenshot()
        if (imageSrc) {
            // Limpiar la URL anterior si existe
            if (photoPreview) {
                MediaService.revokeLocalPreview(photoPreview)
            }
            
            // Forzar la actualización del estado antes de establecer la nueva imagen
            setPhotoPreview(null)
            // Usar setTimeout para asegurar que el estado se actualice
            setTimeout(() => {
                setPhotoPreview(imageSrc)
            }, 0)
            const blob = await (await fetch(imageSrc)).blob()
            const file = new File([blob], 'photo.jpg', { type: 'image/jpeg' })
            setFieldValue('photo', file)
            await checkFace(imageSrc)
            setCameraOpen(false)
        }
    }

    useEffect(() => {
        const load = async () => {
            if (!eventId) return
            try {
                setLoading(true)
                const [e, h] = await Promise.all([
                    EventsService.getEventById(eventId),
                    EventsService.getHierarchicalFilterData(),
                ])
                setEvent(e)
                setHierarchy(h)
            } catch (err) {
                toast.push(
                    <Notification title="Error" type="danger">
                        No se pudo cargar la información
                    </Notification>,
                )
            } finally {
                setLoading(false)
            }
        }
        load()
    }, [eventId])

    const handleSubmit = async (values: AutoRegValues) => {
        if (!eventId) return
        
        // Validar que si hay una imagen, debe tener un rostro detectado
        if (values.photo && faceValid === false) {
            toast.push(
                <Notification title="Error de validación" type="danger">
                    La imagen seleccionada no contiene un rostro válido. Por favor, sube una imagen con rostro o elimina la imagen actual.
                </Notification>,
            )
            return
        }
        
        try {
            setLoading(true)
            
            // Primero validar la existencia del participante
            const validationData = {
                email: values.email,
                firstName: values.firstName,
                lastName: values.lastName,
                phone: values.phone,
                fieldAssignmentId: values.fieldAssignmentId,
                zoneAssignmentId: values.zoneAssignmentId,
                districtAssignmentId: values.districtAssignmentId,
                churchAssignmentId: values.churchAssignmentId,
                ecclesiasticalRole: values.ecclesiasticalRole,
                photo: values.photo,
            }
            
            const validationResult = await EventsService.validateParticipantExists(
                eventId,
                validationData
            )
            
            // Mostrar notificación sobre el estado de validación
            if (validationResult.actionRequired === 'already_registered') {
                toast.push(
                    <Notification title="Información" type="warning">
                        {validationResult.message}
                    </Notification>,
                )
                setSuccess(true)
                return
            }
            
            if (validationResult.actionRequired === 'confirm_update') {
                toast.push(
                    <Notification title="Actualización de datos" type="info">
                        {validationResult.message}
                    </Notification>,
                )
            }
            
            // Proceder con el registro
            const { photo, ...data } = values
            await EventsService.selfRegisterToEvent(eventId, data)
            
            // Mostrar mensaje de éxito contextual
            if (validationResult.existsInSystem && !validationResult.existsInEvent) {
                toast.push(
                    <Notification title="¡Éxito!" type="success">
                        Te has agregado al evento correctamente{validationResult.hasDataDifferences ? ' y tus datos han sido actualizados' : ''}
                    </Notification>,
                )
            } else {
                toast.push(
                    <Notification title="¡Éxito!" type="success">
                        Te has registrado correctamente{validationResult.hasDataDifferences ? ' y tus datos han sido actualizados' : ''}
                    </Notification>,
                )
            }
            
            setSuccess(true)
        } catch (err) {
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo registrar
                </Notification>,
            )
        } finally {
            setLoading(false)
        }
    }

    if (loading && !hierarchy) {
        return (
            <div className="flex justify-center p-8">
                <Loading loading={true} />
            </div>
        )
    }

    const initialValues: AutoRegValues = {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        fieldAssignmentId: null,
        zoneAssignmentId: null,
        districtAssignmentId: null,
        churchAssignmentId: null,
        ecclesiasticalRole: null,
        photo: null,
    }

    return (
        <div className="flex justify-center items-center min-h-screen p-4">
            <Card className="w-full max-w-4xl p-6">
                {success ? (
                    <div className="text-center space-y-4">
                        <HiCheckCircle className="text-emerald-500 w-12 h-12 mx-auto" />
                        <p>¡Registro Exitoso!</p>
                        {authenticated ? (
                            <div className="flex flex-col sm:flex-row justify-center gap-2">
                                <Button variant="solid" onClick={() => window.location.reload()}>
                                    Registrar otro participante
                                </Button>
                                <Button variant="solid" onClick={() => navigate('/events/list')}>
                                    Ir al listado de eventos
                                </Button>
                            </div>
                        ) : (
                            <>
                                <Button variant="solid" onClick={() => window.location.reload()}>
                                    Registrar otro participante
                                </Button>
                                {canCloseWindow ? (
                                    <Button variant="solid" onClick={() => window.close()}>
                                        Cerrar pestaña
                                    </Button>
                                ) : (
                                    <p className="text-sm text-gray-600">
                                        El registro se completó exitosamente. Puede cerrar esta pestaña manualmente.
                                    </p>
                                )}
                            </>
                        )}
                    </div>
                ) : (
                    <div className="space-y-6">
                        <h3 className="text-center text-xl font-semibold">Registro para {event?.title}</h3>
                        <Formik
                            initialValues={initialValues}
                            validationSchema={validationSchema}
                            onSubmit={handleSubmit}
                        >
                            {({ values, errors, touched, setFieldValue, isSubmitting }) => {
                                const zoneOptions = zones.length > 0 ? zones : (hierarchy?.zones || []).filter(z => z.fieldId === values.fieldAssignmentId)
                                const districtOptions = districts.length > 0 ? districts : (hierarchy?.districts || []).filter(d => d.zoneId === values.zoneAssignmentId)
                                const churchOptions = churches.length > 0 ? churches : (hierarchy?.churches || []).filter(c => c.districtId === values.districtAssignmentId)
                                return (
                                    <Form className="space-y-6">
                                        {/* Información Personal */}
                                        <div>
                                            <h4 className="text-lg font-medium mb-4 text-gray-700">Información Personal</h4>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <Field name="firstName" placeholder="Nombre" component={Input} />
                                                    {errors.firstName && touched.firstName && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.firstName}</div>
                                                    )}
                                                </div>
                                                <div>
                                                    <Field name="lastName" placeholder="Apellido" component={Input} />
                                                    {errors.lastName && touched.lastName && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.lastName}</div>
                                                    )}
                                                </div>
                                                <div>
                                                    <Field name="email" type="email" placeholder="Email" component={Input} />
                                                    {errors.email && touched.email && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.email}</div>
                                                    )}
                                                </div>
                                                <div>
                                                    <Field name="phone" placeholder="Teléfono" component={Input} />
                                                    {errors.phone && touched.phone && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.phone}</div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Ubicación Eclesiástica */}
                        <div>
                            <h4 className="text-lg font-medium mb-4 text-gray-700">Ubicación Eclesiástica</h4>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <Select
                                                        options={(hierarchy?.fields || []).map(f => ({ value: f.id, label: f.name }))}
                                                        placeholder="Campo"
                                                        value={values.fieldAssignmentId ? { value: values.fieldAssignmentId, label: String((hierarchy?.fields || []).find(f => f.id === values.fieldAssignmentId)?.name || '') } : null}
                                                        onChange={opt => {
                                                            setFieldValue('fieldAssignmentId', opt?.value ?? null)
                                                            setFieldValue('zoneAssignmentId', null)
                                                            setFieldValue('districtAssignmentId', null)
                                                            setFieldValue('churchAssignmentId', null)
                                                            const newZones = (hierarchy?.zones || []).filter(z => z.fieldId === opt?.value)
                                                            setZones(newZones)
                                                            setDistricts([])
                                                            setChurches([])
                                                        }}
                                                    />
                                                    {errors.fieldAssignmentId && touched.fieldAssignmentId && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.fieldAssignmentId as string}</div>
                                                    )}
                                                </div>
                                                <div>
                                                    <Select
                                                        options={zoneOptions.map(z => ({ value: z.id, label: z.name }))}
                                                        placeholder="Zona"
                                                        isDisabled={!values.fieldAssignmentId}
                                                        value={values.zoneAssignmentId ? { value: values.zoneAssignmentId, label: String(zoneOptions.find(z => z.id === values.zoneAssignmentId)?.name || '') } : null}
                                                        onChange={opt => {
                                                            setFieldValue('zoneAssignmentId', opt?.value ?? null)
                                                        }}
                                                    />
                                                </div>
                                                <div>
                                                    <Select
                                                        options={districtOptions.map(d => ({ value: d.id, label: d.name }))}
                                                        placeholder="Distrito"
                                                        isDisabled={!values.zoneAssignmentId}
                                                        value={values.districtAssignmentId ? { value: values.districtAssignmentId, label: String(districtOptions.find(d => d.id === values.districtAssignmentId)?.name || '') } : null}
                                                        onChange={opt => {
                                                            setFieldValue('districtAssignmentId', opt?.value ?? null)
                                                            setFieldValue('churchAssignmentId', null)
                                                            const newChurches = (hierarchy?.churches || []).filter(c => c.districtId === opt?.value)
                                                            setChurches(newChurches)
                                                        }}
                                                    />
                                                    {errors.districtAssignmentId && touched.districtAssignmentId && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.districtAssignmentId as string}</div>
                                                    )}
                                                </div>
                                                <div>
                                                    <Select
                                                        options={churchOptions.map(c => ({ value: c.id, label: c.name }))}
                                                        placeholder="Iglesia"
                                                        isDisabled={!values.districtAssignmentId}
                                                        value={values.churchAssignmentId ? { value: values.churchAssignmentId, label: String(churchOptions.find(c => c.id === values.churchAssignmentId)?.name || '') } : null}
                                                        onChange={opt => setFieldValue('churchAssignmentId', opt?.value ?? null)}
                                                    />
                                                    {errors.churchAssignmentId && touched.churchAssignmentId && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.churchAssignmentId as string}</div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Cargo Eclesiástico */}
                                        <div>
                                            <h4 className="text-lg font-medium mb-4 text-gray-700">Cargo Eclesiástico</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div>
                                                    <Select
                                                        options={(hierarchy?.ecclesiasticalRoles || []).map(r => ({ value: r, label: r }))}
                                                        placeholder="Cargo"
                                                        value={values.ecclesiasticalRole ? { value: values.ecclesiasticalRole, label: String(values.ecclesiasticalRole) } : null}
                                                        onChange={opt => setFieldValue('ecclesiasticalRole', opt?.value ?? null)}
                                                    />
                                                    {errors.ecclesiasticalRole && touched.ecclesiasticalRole && (
                                                        <div className="text-red-500 text-sm mt-1">{errors.ecclesiasticalRole as string}</div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Foto opcional */}
                                        <div>
                                            <h4 className="text-lg font-medium mb-4 text-gray-700">Foto (opcional)</h4>
                                            <div className="flex items-center space-x-4">
                                                <Upload
                                                    key={photoPreview || 'no-photo'}
                                                    accept="image/*"
                                                    showList={false}
                                                    capture="environment"
                                                    onChange={(files) => handleFileChange(files, setFieldValue)}
                                                    onFileRemove={() => {
                                                        // Limpiar la URL anterior si existe
                                                        if (photoPreview) {
                                                            MediaService.revokeLocalPreview(photoPreview)
                                                        }
                                                        setPhotoPreview(null)
                                                        setFaceValid(null)
                                                        setFieldValue('photo', null)
                                                    }}
                                                >
                                                    {photoPreview ? (
                                                        <img src={photoPreview} alt="preview" className="w-24 h-24 object-cover rounded-full" />
                                                    ) : (
                                                        <div className="w-24 h-24 flex items-center justify-center bg-gray-100 rounded-full text-gray-500">
                                                            Subir
                                                        </div>
                                                    )}
                                                </Upload>
                                                <div className="flex flex-col space-y-2">
                                                    <Button type="button" onClick={() => setCameraOpen(true)}>Tomar foto</Button>
                                                    {photoPreview && (
                                                        <Button 
                                                            type="button" 
                                                            variant="plain" 
                                                            size="sm"
                                                            className="text-red-600 hover:text-red-700"
                                                            onClick={() => {
                                                                // Limpiar la URL anterior si existe
                                                                if (photoPreview) {
                                                                    MediaService.revokeLocalPreview(photoPreview)
                                                                }
                                                                setPhotoPreview(null)
                                                                setFaceValid(null)
                                                                setFieldValue('photo', null)
                                                            }}
                                                        >
                                                            Quitar imagen
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                            {faceValid === false && (
                                                <div className="text-red-500 text-sm mt-2">No se detectó un rostro en la imagen</div>
                                            )}
                                        </div>

                                        {/* Botón de envío */}
                                        <div className="pt-4 text-center">
                                            <Button loading={isSubmitting} variant="solid" type="submit" className="px-8 py-2">
                                                Registrarme
                                            </Button>
                                        </div>
                                        <Dialog isOpen={cameraOpen} onClose={() => setCameraOpen(false)} closable={true}>
                                            <div className="p-4 space-y-4 text-center">
                                                <Webcam ref={webcamRef} screenshotFormat="image/jpeg" />
                                                <Button type="button" variant="solid" onClick={() => capturePhoto(setFieldValue)}>
                                                    Capturar
                                                </Button>
                                            </div>
                                        </Dialog>
                                    </Form>
                                )
                            }}
                        </Formik>
                    </div>
                )}
            </Card>
        </div>
    )
}

export default EventAutoRegistrationView
