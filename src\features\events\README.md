# Módulo de Eventos

Este módulo gestiona la creación y administración de eventos dentro de la aplicación.

## Vistas Principales

- **DashboardView**: resumen estadístico de eventos y asistencia.
- **EventListView**: listado con filtros y exportación de eventos.
- **EventFormView**: formulario para crear o editar eventos.
- **EventDetailView**: detalle completo de un evento con sus sesiones.
- **AttendanceView**: registro manual de asistencia por sesión.
- **FastAttendanceView**: registro rápido tipo kiosko.
- **MyEventsView**: eventos donde el usuario está invitado.

## Flujos Clave

1. **Creación de eventos** a través de *EventFormView*, donde se definen sesiones y participantes.
2. **Registro de asistencia manual** usando *AttendanceView*, pensado para un encargado que marca la presencia de cada participante.
3. **Registro r<PERSON>pid<PERSON> (kiosko)** mediante *FastAttendanceView*, donde los propios participantes se registran de forma autónoma.
4. **Navegación contextual** que permite volver a la pantalla previa utilizando el hook `useNavigationContext`.

Para más detalles sobre la lógica de navegación y los estados de las sesiones consulta `docs/EVENT_WORKFLOWS.md`.
