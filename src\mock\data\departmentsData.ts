import type { Department } from '@/features/events/types'

export const mockDepartments: Department[] = [
    { id: 1, name: 'Presidencia', description: 'Departamento de Presidencia', active: true },
    { id: 2, name: 'Secretaría ejecutiva', description: 'Departamento de Secretaría ejecutiva', active: true },
    { id: 3, name: 'Tesorería', description: 'Departamento de Tesorería', active: true },
    { id: 4, name: 'Contabilidad', description: 'Departamento de Contabilidad', active: true },
    { id: 5, name: 'Ministerios Personales', description: 'Departamento de Ministerios Personales', active: true },
    { id: 6, name: 'Secretaría Ministerial', description: 'Departamento de Secretaría Ministerial', active: true },
    { id: 7, name: 'Minister<PERSON> de Mayordomia', description: 'Departamento de Ministerio de Mayordomia', active: true },
    { id: 8, name: '<PERSON>cursos Humanos', description: 'Departamento de Recursos Humanos', active: true },
    { id: 9, name: 'Minister<PERSON> Juvenil', description: 'Departamento de Ministerio Juvenil', active: true },
    { id: 10, name: 'Minister<PERSON> de la Mujer', description: 'Departamento de Ministerio de la Mujer', active: true },
    { id: 11, name: 'Ministerio Infantil', description: 'Departamento de Ministerio Infantil', active: true },
    { id: 12, name: 'Ministerio de Salud', description: 'Departamento de Ministerio de Salud', active: true },
    { id: 13, name: 'Ministerio de Educación', description: 'Departamento de Ministerio de Educación', active: true },
    { id: 14, name: 'Ministerio de Comunicación', description: 'Departamento de Ministerio de Comunicación', active: true },
    { id: 15, name: 'Ministerio de Música', description: 'Departamento de Ministerio de Música', active: true },
    { id: 16, name: 'Ministerio de Construcción', description: 'Departamento de Ministerio de Construcción', active: true },
    { id: 17, name: 'Ministerio de Capellanía', description: 'Departamento de Ministerio de Capellanía', active: true },
    { id: 18, name: 'Ministerio de Ancianos', description: 'Departamento de Ministerio de Ancianos', active: true },
    { id: 19, name: 'Ministerio de Diáconos', description: 'Departamento de Ministerio de Diáconos', active: true },
    { id: 20, name: 'Ministerio de Diaconisas', description: 'Departamento de Ministerio de Diaconisas', active: true },
    { id: 21, name: 'Ministerio de Escuela Sabática', description: 'Departamento de Ministerio de Escuela Sabática', active: true },
    { id: 22, name: 'Ministerio de Aventureros', description: 'Departamento de Ministerio de Aventureros', active: true },
    { id: 23, name: 'Ministerio de Conquistadores', description: 'Departamento de Ministerio de Conquistadores', active: true },
    { id: 24, name: 'Ministerio de Guías Mayores', description: 'Departamento de Ministerio de Guías Mayores', active: true },
    { id: 25, name: 'Ministerio de Jóvenes Adventistas', description: 'Departamento de Ministerio de Jóvenes Adventistas', active: true },
    { id: 26, name: 'Ministerio de Universitarios Adventistas', description: 'Departamento de Ministerio de Universitarios Adventistas', active: true },
    { id: 27, name: 'Ministerio de Profesionales Adventistas', description: 'Departamento de Ministerio de Profesionales Adventistas', active: true },
    { id: 28, name: 'Ministerio de Familias', description: 'Departamento de Ministerio de Familias', active: true },
    { id: 29, name: 'Ministerio de Solteros', description: 'Departamento de Ministerio de Solteros', active: true },
    { id: 30, name: 'Ministerio de Adultos Mayores', description: 'Departamento de Ministerio de Adultos Mayores', active: true },
]