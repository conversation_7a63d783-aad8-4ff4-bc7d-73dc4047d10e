import { describe, it, expect } from 'vitest'
import { formatDate } from '../dateUtils'

describe('formatDate', () => {
    it('formatea fechas desde Date', () => {
        const date = new Date('2023-05-10T00:00:00Z')
        expect(formatDate(date)).toBe('10/05/2023')
    })

    it('formatea fechas desde string', () => {
        expect(formatDate('2024-01-15')).toBe('15/01/2024')
    })

    it('devuelve cadena vacía para valores inválidos', () => {
        expect(formatDate('invalid')).toBe('')
        expect(formatDate(null)).toBe('')
    })
})
