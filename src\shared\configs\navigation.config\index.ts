import {
    NAV_ITEM_TYPE_TITLE,
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/shared/constants/navigation.constant'
import type { NavigationTree } from '@/shared/@types/navigation'

const navigationConfig: NavigationTree[] = [
    {
        key: 'home',
        path: '/home',
        title: 'Inicio',
        translateKey: 'nav.home',
        icon: 'home',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'eventos',
        path: '/events',
        title: 'Eventos',
        translateKey: 'nav.eventos',
        icon: 'eventos',
        type: NAV_ITEM_TYPE_COLLAPSE,
        expanded: true,
        authority: [],
        subMenu: [
            {
                key: 'eventos',
                path: '/events',
                title: 'Dashboard de Eventos',
                translateKey: 'nav.eventos',
                icon: 'eventos',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'eventos.gestion',
                path: '/events/list',
                title: 'Gestión de Eventos',
                translateKey: 'nav.eventos.gestion',
                icon: 'viewList',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'eventos.registrarAsistencia',
                path: '/events/direct-attendance',
                title: 'Registrar Asistencia',
                translateKey: 'nav.eventos.registrarAsistencia',
                icon: 'userCheck',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'eventos.misEventos',
                path: '/events/my-events',
                title: 'Mis Eventos',
                translateKey: 'nav.eventos.misEventos',
                icon: 'myEvents',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'informes',
        path: '/under-construction/informes',
        title: 'Informes y Análisis de Datos',
        translateKey: 'nav.informes',
        icon: 'informes',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'evaluaciones',
        path: '/under-construction/evaluaciones',
        title: 'Gestión de Evaluaciones',
        translateKey: 'nav.evaluaciones',
        icon: 'evaluaciones',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'autoevaluacion',
        path: '/under-construction/autoevaluacion',
        title: 'Autoevaluación',
        translateKey: 'nav.autoevaluacion',
        icon: 'autoevaluacion',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'planMejora',
        path: '/under-construction/plan-mejora',
        title: 'Plan de Mejora',
        translateKey: 'nav.planMejora',
        icon: 'planMejora',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
]

export default navigationConfig
