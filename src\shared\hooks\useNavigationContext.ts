/**
 * Hook para manejar navegación contextual
 * Permite rastrear el origen de navegación y proporcionar funciones para volver al origen correcto
 */
import { useLocation, useNavigate } from 'react-router-dom'
import { useMemo } from 'react'

export type NavigationOrigin = 'events-list' | 'direct-attendance' | 'event-detail' | 'attendance' | 'fast-attendance' | 'unknown'

export interface NavigationContext {
    origin: NavigationOrigin
    returnPath: string
    eventId?: string
    sessionId?: string
}

/**
 * Hook para manejar la navegación contextual
 * @returns Objeto con funciones y datos de navegación contextual
 */
export const useNavigationContext = () => {
    const location = useLocation()
    const navigate = useNavigate()

    /**
     * Obtiene el contexto de navegación desde el state de la ubicación actual
     */
    const navigationContext = useMemo((): NavigationContext => {
        const state = location.state as any
        
        // Si hay contexto en el state, usarlo
        if (state?.navigationContext) {
            return state.navigationContext
        }

        // Si no hay contexto, intentar inferirlo desde la URL actual o el referrer
        const currentPath = location.pathname
        
        // Determinar origen por defecto basado en patrones de URL
        if (currentPath.includes('/sessions/') && currentPath.includes('/asistencia')) {
            // Estamos en una página de asistencia, el origen por defecto es direct-attendance
            return {
                origin: 'direct-attendance',
                returnPath: '/events/direct-attendance'
            }
        }

        return {
            origin: 'unknown',
            returnPath: '/events/direct-attendance' // Fallback por defecto
        }
    }, [location])

    /**
     * Navega a una página de asistencia preservando el contexto de navegación
     */
    const navigateToAttendance = (
        path: string, 
        origin: NavigationOrigin, 
        returnPath: string,
        eventId?: string,
        sessionId?: string
    ) => {
        const context: NavigationContext = {
            origin,
            returnPath,
            eventId,
            sessionId
        }

        navigate(path, {
            state: {
                navigationContext: context
            }
        })
    }

    /**
     * Navega de vuelta al origen según el contexto
     */
    const navigateBack = () => {
        const { origin, returnPath, eventId } = navigationContext

        switch (origin) {
            case 'events-list':
                navigate('/events/list')
                break
            case 'direct-attendance':
                navigate('/events/direct-attendance')
                break
            case 'event-detail':
                if (eventId) {
                    navigate(`/events/${eventId}`)
                } else {
                    // Fallback si no tenemos eventId
                    navigate('/events/direct-attendance')
                }
                break
            case 'attendance':
            case 'fast-attendance':
                // Para vistas de asistencia, usar el returnPath que contiene la URL completa
                if (returnPath) {
                    navigate(returnPath)
                } else {
                    // Fallback si no tenemos returnPath
                    navigate('/events/direct-attendance')
                }
                break
            case 'unknown':
            default:
                // Usar returnPath como fallback o ir a direct-attendance
                navigate(returnPath || '/events/direct-attendance')
                break
        }
    }

    /**
     * Obtiene el texto apropiado para el botón de volver
     */
    const getBackButtonText = (): string => {
        const { origin } = navigationContext

        switch (origin) {
            case 'events-list':
                return 'Volver a Lista'
            case 'direct-attendance':
                return 'Volver a Asistencia'
            case 'event-detail':
                return 'Volver a Detalles'
            case 'attendance':
                return 'Volver a Registro de Asistencia'
            case 'fast-attendance':
                return 'Volver a Asistencia Rápida'
            case 'unknown':
            default:
                return 'Volver'
        }
    }

    /**
     * Crea un contexto de navegación para pasar a otras páginas
     */
    const createNavigationContext = (
        origin: NavigationOrigin,
        returnPath: string,
        eventId?: string,
        sessionId?: string
    ): NavigationContext => {
        return {
            origin,
            returnPath,
            eventId,
            sessionId
        }
    }

    return {
        navigationContext,
        navigateToAttendance,
        navigateBack,
        getBackButtonText,
        createNavigationContext
    }
}

export default useNavigationContext
