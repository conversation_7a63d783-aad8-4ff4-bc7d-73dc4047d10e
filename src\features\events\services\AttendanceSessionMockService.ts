import type { AttendanceSession } from '../types/attendanceSession'
import { mockMeetings as mockEvents } from '@/mock/data/eventsData'
import type { IAttendanceSessionService } from './AttendanceSessionService.interface'

const AttendanceSessionMockService: IAttendanceSessionService = {
    getSessions(eventId) {
        const event = mockEvents.find(m => m.id.toString() === eventId.toString())
        return Promise.resolve(event?.sessions ?? [])
    },

    createSession(eventId, session) {
        const event = mockEvents.find(m => m.id.toString() === eventId.toString())
        if (!event) return Promise.reject(new Error('Event not found'))
        const newSession: AttendanceSession = {
            ...session,
            id: Date.now().toString(),
            status: session.status || 'pendiente', // Estado por defecto para nuevas sesiones
            attendanceRecords: [],
        }
        if (!event.sessions) event.sessions = []
        event.sessions.push(newSession)
        return Promise.resolve(newSession)
    },

    updateSession(eventId, sessionId, data) {
        const event = mockEvents.find(m => m.id.toString() === eventId.toString())
        const idx = event?.sessions?.findIndex(s => s.id.toString() === sessionId.toString()) ?? -1
        if (!event || idx === -1) return Promise.resolve(null)
        const updated = { ...event.sessions![idx], ...data }
        event.sessions![idx] = updated
        return Promise.resolve(updated)
    },

    deleteSession(eventId, sessionId) {
        const event = mockEvents.find(m => m.id.toString() === eventId.toString())
        if (!event || !event.sessions) return Promise.resolve(false)
        const idx = event.sessions.findIndex(s => s.id.toString() === sessionId.toString())
        if (idx === -1) return Promise.resolve(false)
        event.sessions.splice(idx, 1)
        return Promise.resolve(true)
    },
}

export default AttendanceSessionMockService
