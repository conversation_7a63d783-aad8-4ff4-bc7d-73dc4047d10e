import dayjs from 'dayjs'

export const formatTime = (timeValue: string | Date | unknown): string => {
    if (!timeValue) return 'No especificado'

    let timeString: string

    if (timeValue instanceof Date) {
        const hours = timeValue.getHours().toString().padStart(2, '0')
        const minutes = timeValue.getMinutes().toString().padStart(2, '0')
        timeString = `${hours}:${minutes}`
    } else if (typeof timeValue === 'string') {
        timeString = timeValue
    } else {
        console.warn('formatTime received an unexpected type:', timeValue)
        return 'Hora inválida'
    }

    try {
        const [hours, minutes] = timeString.split(':')
        if (hours === undefined || minutes === undefined) {
            console.warn('formatTime received an invalid time string format:', timeString)
            return 'Hora inválida'
        }
        const hour = parseInt(hours, 10)
        if (isNaN(hour)) {
            console.warn('formatTime could not parse hour:', hours)
            return 'Hora inválida'
        }
        const ampm = hour >= 12 ? 'PM' : 'AM'
        const hour12 = hour % 12 || 12
        return `${hour12.toString().padStart(2, '0')}:${minutes} ${ampm}`
    } catch (e) {
        console.error('Error splitting time string in formatTime:', timeString, e)
        return 'Hora inválida'
    }
}

export const parseTimeString = (
    timeString: string | null | undefined,
): Date | null => {
    if (!timeString) return null

    const [hours, minutes] = timeString.split(':')
    const h = parseInt(hours, 10)
    const m = parseInt(minutes, 10)

    if (Number.isNaN(h) || Number.isNaN(m)) {
        console.warn('parseTimeString received invalid value:', timeString)
        return null
    }

    return dayjs().hour(h).minute(m).second(0).millisecond(0).toDate()
}


