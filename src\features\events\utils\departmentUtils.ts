/**
 * Utilidades para el manejo de departamentos
 */
import type { Department } from '../types';

/**
 * Obtiene el nombre de un departamento basándose en su ID
 * @param departmentId - ID del departamento
 * @param departments - Lista de departamentos disponibles
 * @returns El nombre del departamento o 'No especificado' si no se encuentra
 */
export const getDepartmentNameById = (
    departmentId: string | number | undefined,
    departments: Department[]
): string => {
    if (!departmentId || !departments.length) {
        return 'No especificado';
    }

    const department = departments.find(dept => 
        dept.id.toString() === departmentId.toString()
    );

    return department?.name || 'No especificado';
};

/**
 * Obtiene un departamento completo basándose en su ID
 * @param departmentId - ID del departamento
 * @param departments - Lista de departamentos disponibles
 * @returns El departamento completo o null si no se encuentra
 */
export const getDepartmentById = (
    departmentId: string | number | undefined,
    departments: Department[]
): Department | null => {
    if (!departmentId || !departments.length) {
        return null;
    }

    return departments.find(dept => 
        dept.id.toString() === departmentId.toString()
    ) || null;
};