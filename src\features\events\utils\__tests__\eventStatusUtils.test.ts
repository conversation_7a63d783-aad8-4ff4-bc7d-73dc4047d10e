import { describe, it, expect } from 'vitest'
import {
    canCompleteEvent,
    determineEventStatus,
    calculateEventProgress,
    getPendingSessions,
    getCompletedSessions,
    getInProgressSessions,
    canChangeSessionStatus,
    getSessionStatusMessage,
    getEventProgressMessage,
    canRecordAttendance,
    getSessionsRequiringAttention
} from '../eventStatusUtils'
import type { Event, AttendanceSessionStatus } from '../../types'

const makeSession = (id: string, status: AttendanceSessionStatus) => ({
    id,
    date: '2023-01-01',
    attendanceMode: 'normal',
    isDefault: id === 's1',
    status,
    attendanceRecords: []
})

const createEvent = (statuses: AttendanceSessionStatus[], eventStatus: string = 'programada'): Event => ({
    id: 1,
    title: 'E',
    date: '2023-01-01',
    startTime: '10:00',
    location: 'X',
    eventStatus: eventStatus as any,
    participantsInvited: [],
    sessions: statuses.map((s, i) => makeSession(`s${i+1}`, s))
})

describe('eventStatusUtils', () => {
    it('canCompleteEvent', () => {
        expect(canCompleteEvent(createEvent(['completada', 'completada']))).toBe(true)
        expect(canCompleteEvent(createEvent(['completada', 'pendiente']))).toBe(false)
    })

    it('determineEventStatus', () => {
        expect(determineEventStatus(createEvent(['completada']))).toBe('completada')
        expect(determineEventStatus(createEvent(['cancelada', 'cancelada']))).toBe('cancelada')
        expect(determineEventStatus(createEvent(['en_progreso', 'pendiente']))).toBe('en-progreso')
        expect(determineEventStatus(createEvent(['pendiente']))).toBe('programada')
    })

    it('calculateEventProgress', () => {
        const event = createEvent(['completada', 'pendiente', 'completada'])
        expect(calculateEventProgress(event)).toBe(67)
    })

    it('session getters', () => {
        const event = createEvent(['pendiente', 'en_progreso', 'completada'])
        expect(getPendingSessions(event).length).toBe(1)
        expect(getInProgressSessions(event).length).toBe(1)
        expect(getCompletedSessions(event).length).toBe(1)
    })

    it('canChangeSessionStatus', () => {
        expect(canChangeSessionStatus('pendiente', 'en_progreso')).toBe(true)
        expect(canChangeSessionStatus('completada', 'pendiente')).toBe(false)
    })

    it('getSessionStatusMessage', () => {
        expect(getSessionStatusMessage('en_progreso')).toBe('Sesión en progreso')
    })

    it('getEventProgressMessage', () => {
        expect(getEventProgressMessage(createEvent([]))).toBe('Evento sin sesiones de asistencia')
        expect(getEventProgressMessage(createEvent(['completada']))).toBe('Todas las sesiones completadas')
    })

    it('canRecordAttendance', () => {
        expect(canRecordAttendance('en_progreso')).toBe(true)
        expect(canRecordAttendance('cancelada')).toBe(false)
    })

    it('getSessionsRequiringAttention', () => {
        const event = createEvent(['pendiente', 'completada', 'en_progreso'])
        expect(getSessionsRequiringAttention(event).length).toBe(2)
    })
})
