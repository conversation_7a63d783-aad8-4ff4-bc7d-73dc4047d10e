import type { AttendanceRecord } from './index'

/**
 * Estados posibles de una sesión de asistencia
 */
export type AttendanceSessionStatus = 'pendiente' | 'en_progreso' | 'completada' | 'cancelada';

/**
 * Define la estructura de una sesión de asistencia dentro de un evento.
 */
export interface AttendanceSession {
    id: string | number
    date: string
    comment?: string
    attendanceMode: 'normal' | 'kiosk'
    isDefault: boolean
    status: AttendanceSessionStatus
    attendanceRecords?: AttendanceRecord[]
}
