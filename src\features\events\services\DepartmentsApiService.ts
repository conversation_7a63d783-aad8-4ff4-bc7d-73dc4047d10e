import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type { Department, DepartmentStats } from '../types'
import type { IDepartmentsService } from './DepartmentsService.interface'
import type { PaginatedResponse } from '@/shared/types/api'

/**
 * Servicio que consume la API real para todas las operaciones relacionadas
 * con departamentos.
 */
class DepartmentsApiService implements IDepartmentsService {
    /**
     * Obtiene todos los departamentos desde la API con paginación
     */
    async getDepartments(): Promise<PaginatedResponse<Department>> {
        try {
            const response = await ApiService.fetchData<PaginatedResponse<Department>>({
                url: API_ENDPOINTS.DEPARTMENTS.BASE,
                method: 'get',
            })
            return response.data
        } catch (error) {
            console.error('Error al obtener departamentos:', error)
            throw error
        }
    }

    /**
     * Obtiene solo los departamentos activos desde la API con paginación
     */
    async getActiveDepartments(): Promise<PaginatedResponse<Department>> {
        try {
            const response = await ApiService.fetchData<PaginatedResponse<Department>>({
                url: API_ENDPOINTS.DEPARTMENTS.ACTIVE,
                method: 'get',
            })
            return response.data
        } catch (error) {
            console.error('Error al obtener departamentos activos:', error)
            throw error
        }
    }

    /**
     * Obtiene un departamento por su identificador
     * @param id - ID del departamento
     */
    async getDepartmentById(id: string): Promise<Department> {
        try {
            const response = await ApiService.fetchData<Department>({
                url: API_ENDPOINTS.DEPARTMENTS.BY_ID(id),
                method: 'get',
            })
            return response.data
        } catch (error) {
            console.error('Error al obtener departamento por ID:', error)
            throw error
        }
    }

    /**
     * Obtiene estadísticas de un departamento
     * @param id - ID del departamento
     */
    async getDepartmentStats(id: string): Promise<DepartmentStats> {
        try {
            const response = await ApiService.fetchData<DepartmentStats>({
                url: API_ENDPOINTS.DEPARTMENTS.STATS(id),
                method: 'get',
            })
            return response.data
        } catch (error) {
            console.error('Error al obtener estadísticas del departamento:', error)
            throw error
        }
    }
}

export default new DepartmentsApiService()