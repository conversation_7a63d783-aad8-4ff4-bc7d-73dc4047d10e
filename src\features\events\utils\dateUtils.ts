/**
 * Formatea una fecha al formato DD/MM/YYYY.
 * @param dateInput - Fecha a formatear.
 * @returns Cadena formateada o vacía si la fecha es inválida.
 */
export const formatDate = (dateInput: string | Date | null | undefined) => {
    if (!dateInput) return ''
    try {
        const date =
            typeof dateInput === 'string' ? new Date(dateInput) : dateInput
        if (isNaN(date.getTime())) return ''
        const day = String(date.getDate()).padStart(2, '0')
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const year = date.getFullYear()
        return `${day}/${month}/${year}`
    } catch (err) {
        console.error('formatDate error:', err)
        return ''
    }
}
