import { mockMeetings as mockEvents } from '@/mock/data/eventsData'
import { mockEventTypes } from '@/mock/data/eventTypesData'
import {
    mockMeetingParticipants as mockParticipants,
    mockHierarchicalData as mockHierarchicalFilterData,
} from '@/mock/data/churchUsersData'
import { ensureParticipantIds, sanitizeEvent } from '../utils/eventDataUtils'
import type {
    Event,
    EventParticipant,
    EcclesiasticalUnit,
    HierarchicalFilterData,
    AttendanceRecord,
    EventStatus,
} from '../types'
import type { IEventsService } from './EventsService.interface'
import type { PaginatedResponse } from '@/shared/types/api'

/**
 * Implementación mock que gestiona todas las operaciones de eventos
 * utilizando únicamente datos simulados. Útil para desarrollo y pruebas
 * sin depender del backend.
 */
const EventsMockService: IEventsService = {
    /** Devuelve la lista de eventos disponibles con formato paginado */
    getEvents() {
        const sanitizedEvents = mockEvents.map((e) => sanitizeEvent(e))
        
        // Simular respuesta paginada de Strapi
        const paginatedResponse: PaginatedResponse<Event> = {
            data: sanitizedEvents,
            meta: {
                pagination: {
                    page: 1,
                    pageSize: sanitizedEvents.length,
                    pageCount: 1,
                    total: sanitizedEvents.length
                }
            }
        }
        
        return Promise.resolve(paginatedResponse)
    },

    /** Obtiene un evento por su ID */
    getEventById(id) {
        const event = mockEvents.find((m) => m.id.toString() === id.toString())
        return Promise.resolve(event ? sanitizeEvent(event) : null)
    },

    /** Crea un nuevo evento en memoria */
    createEvent(eventData) {
        const newEvent: Event = sanitizeEvent({
            ...(eventData as Event),
            id: Date.now().toString(),
            attendanceRecords: [],
        })
        mockEvents.push(newEvent)
        return Promise.resolve(newEvent)
    },

    /** Actualiza un evento existente */
    updateEvent(id, eventData) {
        const eventIndex = mockEvents.findIndex(
            (m) => m.id.toString() === id.toString(),
        )
        if (eventIndex === -1) {
            return Promise.resolve(null)
        }
        const updatedEvent: Event = sanitizeEvent({
            ...mockEvents[eventIndex],
            ...(eventData as Event),
        })
        mockEvents[eventIndex] = updatedEvent
        return Promise.resolve(updatedEvent)
    },

    /** Elimina un evento de la lista mock */
    deleteEvent(id) {
        const eventIndex = mockEvents.findIndex(
            (m) => m.id.toString() === id.toString(),
        )
        if (eventIndex === -1) {
            return Promise.resolve(false)
        }
        mockEvents.splice(eventIndex, 1)
        return Promise.resolve(true)
    },

    /** Devuelve los participantes simulados */
    getParticipants() {
        return Promise.resolve([...mockParticipants])
    },
    /** Devuelve los tipos de evento disponibles */
    getEventTypes() {
        return Promise.resolve([...mockEventTypes])
    },

    /** Importa participantes a un evento en memoria */
    importParticipants(eventId, participants) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve(false)
        }
        const sanitized = ensureParticipantIds(participants)
        sanitized.forEach((p) => {
            const exists = event.participantsInvited.some(
                (inv) => inv.id.toString() === p.id.toString(),
            )
            if (!exists) {
                event.participantsInvited.push(p)
            }
        })
        return Promise.resolve(true)
    },

    /** Permite a un usuario registrarse a sí mismo en un evento */
    selfRegisterToEvent(eventId, participantData) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve(false)
        }

        let participant = mockParticipants.find(
            (p) =>
                p.email.toLowerCase() === participantData.email.toLowerCase(),
        )

        if (!participant) {
            participant = {
                id: Date.now().toString(),
                ...participantData,
            } as EventParticipant
            mockParticipants.push(participant)
        }

        const exists = event.participantsInvited.some(
            (p) => p.email.toLowerCase() === participant!.email.toLowerCase(),
        )

        if (!exists) {
            event.participantsInvited.push(participant)
        }

        return Promise.resolve(true)
    },

    registerParticipantAndMarkAttendance(eventId, participantData) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve(false)
        }

        let participant = mockParticipants.find(
            (p) =>
                p.email.toLowerCase() === participantData.email.toLowerCase(),
        )

        if (!participant) {
            participant = {
                id: Date.now().toString(),
                ...participantData,
            } as EventParticipant
            mockParticipants.push(participant)
        }

        const exists = event.participantsInvited.some(
            (p) => p.email.toLowerCase() === participant!.email.toLowerCase(),
        )

        if (!exists) {
            event.participantsInvited.push(participant)
        }

        const session =
            event.sessions.find((s) => s.isDefault) || event.sessions[0]
        if (!session) {
            return Promise.resolve(false)
        }

        if (!session.attendanceRecords) {
            session.attendanceRecords = []
        }

        const already = session.attendanceRecords.some(
            (r) => r.person.id.toString() === participant!.id.toString(),
        )
        if (!already) {
            const record: AttendanceRecord = {
                id: Date.now().toString(),
                sessionId: session.id,
                person: {
                    id: participant.id,
                    firstName: participant.firstName || '',
                    lastName: participant.lastName || '',
                    ecclesiasticalRole: participant.ecclesiasticalRole,
                    email: participant.email,
                    avatar: participant.avatar,
                },
                attended: true,
            }
            session.attendanceRecords.push(record)
        }

        return Promise.resolve(true)
    },

    /** Registra en memoria la asistencia de un participante */
    recordAttendance(eventId, sessionId, participantId, attended, notes) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        const session = event?.sessions?.find(
            (s) => s.id.toString() === sessionId.toString(),
        )
        let participant = mockParticipants.find(
            (p) => p.id.toString() === participantId.toString(),
        )

        // Fallback: buscar en los participantes invitados del evento
        if (!participant) {
            participant = event?.participantsInvited.find(
                (p) => p.id.toString() === participantId.toString(),
            ) as EventParticipant | undefined
        }

        if (!event || !session || !participant) {
            return Promise.resolve(null)
        }

        if (!session.attendanceRecords) {
            session.attendanceRecords = []
        }

        const existingRecordIndex = session.attendanceRecords.findIndex(
            (record) =>
                record.person.id.toString() === participantId.toString(),
        )

        const recordData: AttendanceRecord = {
            id:
                existingRecordIndex >= 0
                    ? session.attendanceRecords[existingRecordIndex].id
                    : Date.now().toString(),
            sessionId,
            person: {
                id: participant.id,
                firstName: participant.firstName || '',
                lastName: participant.lastName || '',
                ecclesiasticalRole: participant.ecclesiasticalRole,
                email: participant.email,
                avatar: participant.avatar,
            },
            attended,
            notes,
        }

        // Si existe un registro, actualizarlo; si no, crear uno nuevo
        if (existingRecordIndex >= 0) {
            session.attendanceRecords[existingRecordIndex] = recordData
        } else {
            session.attendanceRecords.push(recordData)
        }

        const newRecord = recordData
        return Promise.resolve(newRecord)
    },

    /** Calcula estadísticas básicas para el tablero */
    getDashboardStats() {
        const totalProgramadas = mockEvents.filter(
            (m) => m.eventStatus === 'programada',
        ).length

        let totalAttendedRecords = 0
        let totalAttendanceRecords = 0

        mockEvents.forEach((event) => {
            event.sessions?.forEach((session) => {
                if (
                    session.attendanceRecords &&
                    session.attendanceRecords.length > 0
                ) {
                    totalAttendanceRecords += session.attendanceRecords.length
                    totalAttendedRecords += session.attendanceRecords.filter(
                        (record) => record.attended,
                    ).length
                }
            })
        })

        const promedioAsistencia =
            totalAttendanceRecords > 0
                ? Math.round(
                      (totalAttendedRecords / totalAttendanceRecords) * 100,
                  )
                : 0

        const today = new Date()
        today.setHours(0, 0, 0, 0)

        const proximosEventos = mockEvents
            .filter((m) => {
                const eventDate = new Date(m.date)
                return eventDate >= today && m.eventStatus === 'programada'
            })
            .sort(
                (a, b) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime(),
            )

        const proximoEvento =
            proximosEventos.length > 0
                ? {
                      title: proximosEventos[0].title,
                      date: proximosEventos[0].date,
                  }
                : undefined

        const eventosPorEstado = [
            {
                status: 'programada' as EventStatus,
                count: mockEvents.filter((m) => m.eventStatus === 'programada')
                    .length,
            },
            {
                status: 'en-progreso' as EventStatus,
                count: mockEvents.filter((m) => m.eventStatus === 'en-progreso')
                    .length,
            },
            {
                status: 'completada' as EventStatus,
                count: mockEvents.filter((m) => m.eventStatus === 'completada')
                    .length,
            },
            {
                status: 'cancelada' as EventStatus,
                count: mockEvents.filter((m) => m.eventStatus === 'cancelada')
                    .length,
            },
        ]

        const eventosPorDeptoMap: Record<string, number> = mockEvents.reduce(
            (acc, m) => {
                const depto = m.invitingDepartment || 'No Especificado'
                acc[depto] = (acc[depto] || 0) + 1
                return acc
            },
            {} as Record<string, number>,
        )

        const eventosPorDepartamento = Object.entries(
            eventosPorDeptoMap,
        ).map(([departamento, count]) => ({
            departamento,
            count,
        }))

        return Promise.resolve({
            totalProgramadas,
            promedioAsistencia,
            proximaReunion: proximoEvento,
            eventosPorEstado,
            eventosPorDepartamento,
        })
    },

    /** Obtiene las unidades jerárquicas para los filtros */
    getHierarchicalFilterData() {
        return Promise.resolve(mockHierarchicalFilterData)
    },

    /** Devuelve los eventos de un usuario y su asistencia */
    getEventsForUser(userId) {
        const userEvents = mockEvents.filter((event) =>
            event.participantsInvited.some(
                (participant) =>
                    participant.id.toString() === userId.toString(),
            ),
        )

        const eventsWithUserRecord = userEvents.map((event) => {
            const defaultSession =
                event.sessions?.find((s) => s.isDefault) || event.sessions?.[0]
            const userRecord = defaultSession?.attendanceRecords?.find(
                (record) => record.person.id.toString() === userId.toString(),
            )
            return {
                ...event,
                userAttendanceRecord: userRecord,
            }
        })

        return Promise.resolve(eventsWithUserRecord)
    },

    /** Guarda una justificación de ausencia */
    justifyAbsence(eventId, userId, justification) {
        const eventIndex = mockEvents.findIndex(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (eventIndex === -1) {
            return Promise.resolve(false)
        }

        const event = mockEvents[eventIndex]
        const isInvited = event.participantsInvited.some(
            (p) => p.id.toString() === userId.toString(),
        )
        if (!isInvited) {
            return Promise.resolve(false)
        }

        const defaultSession =
            event.sessions?.find((s) => s.isDefault) || event.sessions?.[0]
        if (!defaultSession) return Promise.resolve(false)
        let recordIndex = -1
        if (defaultSession.attendanceRecords) {
            recordIndex = defaultSession.attendanceRecords.findIndex(
                (record) => record.person.id.toString() === userId.toString(),
            )
        } else {
            defaultSession.attendanceRecords = []
        }

        let userParticipant = event.participantsInvited.find(
            (p) => p.id.toString() === userId.toString(),
        )
        if (!userParticipant) {
            userParticipant = mockParticipants.find(
                (p) => p.id.toString() === userId.toString(),
            )
            if (!userParticipant) {
                return Promise.resolve(false)
            }
        }

        if (recordIndex >= 0) {
            defaultSession.attendanceRecords[recordIndex] = {
                ...defaultSession.attendanceRecords[recordIndex],
                attended: false,
                notes: justification,
            }
        } else {
            const newRecord: AttendanceRecord = {
                id: Date.now().toString(),
                sessionId: defaultSession.id,
                person: {
                    id: userParticipant.id,
                    firstName: userParticipant.firstName || '',
                    lastName: userParticipant.lastName || '',
                    ecclesiasticalRole: userParticipant.ecclesiasticalRole,
                    email: userParticipant.email,
                    avatar: userParticipant.avatar,
                },
                attended: false,
                notes: justification,
            }
            defaultSession.attendanceRecords.push(newRecord)
        }

        return Promise.resolve(true)
    },

    /** Actualiza el estado de un evento */
    updateEventStatus(eventId, status) {
        const eventIndex = mockEvents.findIndex(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (eventIndex === -1) {
            return Promise.resolve(false)
        }
        mockEvents[eventIndex].eventStatus = status
        return Promise.resolve(true)
    },

    /** Actualiza el estado de una sesión específica */
    updateSessionStatus(eventId, sessionId, status) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve(false)
        }

        const session = event.sessions.find(
            (s) => s.id.toString() === sessionId.toString(),
        )
        if (!session) {
            return Promise.resolve(false)
        }

        session.status = status
        return Promise.resolve(true)
    },

    /** Obtiene el estado de una sesión específica */
    getSessionStatus(eventId, sessionId) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve(null)
        }

        const session = event.sessions.find(
            (s) => s.id.toString() === sessionId.toString(),
        )
        if (!session) {
            return Promise.resolve(null)
        }

        return Promise.resolve(session.status)
    },

    /** Completa automáticamente una sesión en modo kiosco */
    completeKioskSession(eventId, sessionId) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve(false)
        }

        const session = event.sessions.find(
            (s) => s.id.toString() === sessionId.toString(),
        )
        if (!session || session.attendanceMode !== 'kiosk') {
            return Promise.resolve(false)
        }

        // Marcar como "no asistió" a todos los participantes que no tienen registro de asistencia
        const existingRecords = session.attendanceRecords || []
        const recordedParticipantIds = existingRecords.map((record) =>
            record.person.id.toString(),
        )

        event.participantsInvited.forEach((participant) => {
            const participantId = participant.id.toString()
            if (!recordedParticipantIds.includes(participantId)) {
                // Crear registro de "no asistió" para participantes sin registro
                const newRecord = {
                    id: `auto-${Date.now()}-${participantId}`,
                    sessionId: sessionId,
                    person: {
                        id: participant.id,
                        firstName: participant.firstName,
                        lastName: participant.lastName,
                        ecclesiasticalRole: participant.ecclesiasticalRole,
                        avatar: participant.avatar,
                        email: participant.email,
                    },
                    attended: false,
                    notes: 'Marcado automáticamente como ausente al completar sesión de kiosco',
                }

                if (!session.attendanceRecords) {
                    session.attendanceRecords = []
                }
                session.attendanceRecords.push(newRecord)
            }
        })

        // Marcar la sesión como completada
        session.status = 'completada'
        return Promise.resolve(true)
    },

    /** Valida la existencia de un participante antes del registro */
    validateParticipantExists(eventId, participantData) {
        const event = mockEvents.find(
            (m) => m.id.toString() === eventId.toString(),
        )
        if (!event) {
            return Promise.resolve({
                existsInSystem: false,
                existsInEvent: false,
                hasDataDifferences: false,
                hasExistingPhoto: false,
                willUpdatePhoto: false,
                message: 'Evento no encontrado',
                actionRequired: 'none' as const,
            })
        }

        // Buscar participante existente por email
        const existingParticipant = mockParticipants.find(
            (p) => p.email.toLowerCase() === participantData.email.toLowerCase(),
        )

        const existsInSystem = !!existingParticipant
        const existsInEvent = existsInSystem && event.participantsInvited.some(
            (p) => p.email.toLowerCase() === participantData.email.toLowerCase(),
        )

        if (!existsInSystem) {
            return Promise.resolve({
                existsInSystem: false,
                existsInEvent: false,
                hasDataDifferences: false,
                hasExistingPhoto: false,
                willUpdatePhoto: !!participantData.photo,
                message: 'Participante nuevo - se registrará en el sistema',
                actionRequired: 'none' as const,
            })
        }

        // Comparar datos para detectar diferencias
        const dataDifferences = {
            fieldAssignment: existingParticipant.fieldAssignmentId !== participantData.fieldAssignmentId,
            zoneAssignment: existingParticipant.zoneAssignmentId !== participantData.zoneAssignmentId,
            districtAssignment: existingParticipant.districtAssignmentId !== participantData.districtAssignmentId,
            churchAssignment: existingParticipant.churchAssignmentId !== participantData.churchAssignmentId,
            ecclesiasticalRole: existingParticipant.ecclesiasticalRole !== participantData.ecclesiasticalRole,
            phone: existingParticipant.phone !== participantData.phone,
            firstName: existingParticipant.firstName !== participantData.firstName,
            lastName: existingParticipant.lastName !== participantData.lastName,
        }

        const hasDataDifferences = Object.values(dataDifferences).some(Boolean)
        const hasExistingPhoto = !!existingParticipant.avatar
        const willUpdatePhoto = !!participantData.photo

        let message = ''
        let actionRequired: 'none' | 'confirm_update' | 'already_registered' = 'none'

        if (existsInEvent) {
            if (hasDataDifferences || willUpdatePhoto) {
                message = 'El participante ya está registrado en este evento. Se detectaron diferencias en los datos que se actualizarán.'
                actionRequired = 'confirm_update'
            } else {
                message = 'El participante ya está registrado en este evento con los mismos datos.'
                actionRequired = 'already_registered'
            }
        } else {
            if (hasDataDifferences || willUpdatePhoto) {
                message = 'El participante existe en el sistema. Se agregará al evento y se actualizarán los datos que difieren.'
                actionRequired = 'confirm_update'
            } else {
                message = 'El participante existe en el sistema. Se agregará al evento.'
                actionRequired = 'none'
            }
        }

        return Promise.resolve({
            existsInSystem,
            existsInEvent,
            existingParticipant,
            hasDataDifferences,
            dataDifferences: hasDataDifferences ? dataDifferences : undefined,
            hasExistingPhoto,
            willUpdatePhoto,
            message,
            actionRequired,
        })
    },
}

export default EventsMockService
