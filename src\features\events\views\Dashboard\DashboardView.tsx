/**
 * Componente Dashboard de Eventos
 * Muestra estadísticas y gráficos relacionados con los eventos
 */
import { useState, useEffect, useMemo, useRef } from 'react' // Se importa useMemo para optimizar cálculos
import Container from '@/shared/components/shared/Container'
import AdaptableCard from '@/shared/components/shared/AdaptableCard'
import Chart from '@/shared/components/shared/Chart'
import { Card, Button, toast, Notification } from '@/shared/components/ui'
import EventsService from '../../services/EventsService'
import type { EventStatus } from '../../types'

// Importamos íconos para las tarjetas
import {
    HiCalendar,
    HiCheckCircle,
    HiUserGroup,
    HiDownload,
} from 'react-icons/hi'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import * as XLSX from 'xlsx'
import { mockMeetingParticipants } from '@/mock/data/churchUsersData'

/**
 * Componente principal del Dashboard de Eventos
 * Muestra tarjetas con estadísticas y gráficos
 */
const DashboardView = () => {
    // Simulamos un usuario logueado para el prototipo
    const loggedInUserId = mockMeetingParticipants[0].id
    // Referencia al contenedor del dashboard para exportar a PDF
    const dashboardRef = useRef<HTMLDivElement>(null)
    // Estado para almacenar los datos del dashboard
    const [dashboardData, setDashboardData] = useState<{
        totalProgramadas: number
        promedioAsistencia: number
        proximaReunion?: { title: string; date: string }
        eventosPorEstado: Array<{ status: EventStatus; count: number }>
    eventosPorDepartamento: Array<{ departamento: string; count: number }>
    } | null>(null)

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)

    // Función para formatear el estado del evento
    const formatStatus = (status: EventStatus): string => {
        const statusMap: Record<EventStatus, string> = {
            programada: 'Programada',
            'en-progreso': 'En Progreso',
            completada: 'Completada',
            cancelada: 'Cancelada',
        }
        return statusMap[status] || status
    }

    // Función para exportar datos a Excel
    const handleExportExcel = async () => {
        try {
            setLoading(true)

            // Obtener todos los eventos
            const allEvents = await EventsService.getEvents()

            if (!allEvents || allEvents.length === 0) {
                toast.push(
                    <Notification title="Sin datos" type="info">
                        No hay eventos para exportar.
                    </Notification>,
                )
                return
            }

            const dataForExcel = allEvents.map((event) => {
                const attendanceSummary = event.attendanceRecords?.reduce(
                    (acc, record) => {
                        if (record.attended) acc.attended++
                        else acc.absent++
                        if (record.partialAttendance) acc.partial++
                        return acc
                    },
                    { attended: 0, absent: 0, partial: 0 },
                ) || { attended: 0, absent: 0, partial: 0 }

                return {
                    ID: event.id,
                    Título: event.title,
                    Fecha: event.date,
                    Estado: event.eventStatus,
                    Asistentes: attendanceSummary.attended,
                    Ausentes: attendanceSummary.absent,
                }
            })

            // Crear archivo Excel
            const worksheet = XLSX.utils.json_to_sheet(dataForExcel)
            const workbook = XLSX.utils.book_new()
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Dashboard Eventos')
            XLSX.writeFile(workbook, 'DashboardEventos.xlsx')

            toast.push(
                <Notification title="Exportación Completa" type="success">
                    El archivo Excel DashboardEventos.xlsx ha sido descargado.
                </Notification>,
            )
        } catch (error) {
            console.error('Error al exportar eventos:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    Ocurrió un error al exportar los eventos. Por favor,
                    inténtelo de nuevo.
                </Notification>,
            )
        } finally {
            setLoading(false)
        }
    }

    // Función para exportar el dashboard a PDF
    const handleExportPDF = async () => {
        if (!dashboardRef.current) return;
        try {
            const canvas = await html2canvas(dashboardRef.current, { useCORS: true });
            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF('p', 'mm', 'a4');
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
            pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
            pdf.save('dashboard.pdf');
        } catch (error) {
            console.error('Error al exportar PDF:', error);
        }
    }

    // Efecto para cargar los datos del dashboard al montar el componente
    useEffect(() => {
        const fetchDashboardData = async () => {
            try {
                const data = await EventsService.getDashboardStats()
                setDashboardData(data)
            } catch (error) {
                console.error('Error al cargar datos del dashboard:', error)
            } finally {
                setLoading(false)
            }
        }

        fetchDashboardData()
    }, [])

    // Calcular el total de eventos completados
        const totalEventosCompletados =
            dashboardData?.eventosPorEstado.find(
                (item) => item.status === 'completada',
            )?.count || 0

    // Preparar datos para el gráfico de donut
    const donutData =
        dashboardData?.eventosPorEstado.map((item) => ({
            name: formatStatus(item.status),
            value: item.count,
        })) || []

    const series = donutData.map((item) => item.value)
    const labels = donutData.map((item) => item.name)


    // Preparar opciones para el gráfico de barras
    const barChartCustomOptions = useMemo(() => {
        if (
            dashboardData?.eventosPorDepartamento &&
            Array.isArray(dashboardData.eventosPorDepartamento) &&
            dashboardData.eventosPorDepartamento.length > 0
        ) {
            return {
                xaxis: {
                    categories: dashboardData.eventosPorDepartamento.map(
                        (item) => String(item.departamento || ''),
                    ),
                },
                // Otras opciones si son necesarias para el gráfico de barras
            }
        }
        return null // Retorna null si no hay datos válidos
    }, [dashboardData?.eventosPorDepartamento])

    return (
        <Container ref={dashboardRef}>
            <div className="mb-6 flex justify-end gap-2">
                <Button
                    size="sm"
                    variant="solid"
                    icon={<HiDownload />}
                    onClick={handleExportExcel}
                >
                    Exportar Excel
                </Button>
                <Button
                    size="sm"
                    variant="solid"
                    icon={<HiDownload />}
                    onClick={handleExportPDF}
                >
                    Exportar Dashboard a PDF
                </Button>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                {/* Tarjeta: Eventos Programados */}
                <AdaptableCard>
                    <div className="p-4">
                        <h5 className="mb-4 text-lg font-semibold">
                            Eventos Programados
                        </h5>
                        {loading ? (
                            <div className="animate-pulse h-12 bg-gray-200 rounded"></div>
                        ) : (
                            <div className="flex items-center">
                                <div className="mr-4 p-3 rounded-full bg-blue-100 text-blue-500">
                                    <HiCalendar className="text-xl" />
                                </div>
                                <span className="text-3xl font-bold">
                                    {dashboardData?.totalProgramadas || 0}
                                </span>
                            </div>
                        )}
                    </div>
                </AdaptableCard>

                {/* Tarjeta: Eventos Realizados */}
                <AdaptableCard>
                    <div className="p-4">
                        <h5 className="mb-4 text-lg font-semibold">
                            Eventos Realizados
                        </h5>
                        {loading ? (
                            <div className="animate-pulse h-12 bg-gray-200 rounded"></div>
                        ) : (
                            <div className="flex items-center">
                                <div className="mr-4 p-3 rounded-full bg-green-100 text-green-500">
                                    <HiCheckCircle className="text-xl" />
                                </div>
                                <span className="text-3xl font-bold">
                                    {totalEventosCompletados}
                                </span>
                            </div>
                        )}
                    </div>
                </AdaptableCard>

                {/* Tarjeta: Índice de Asistencia en Eventos Realizados */}
                <AdaptableCard>
                    <div className="p-4">
                        <h5 className="mb-4 text-lg font-semibold">
                            Índice de Asistencia
                        </h5>
                        {loading ? (
                            <div className="animate-pulse h-12 bg-gray-200 rounded"></div>
                        ) : (
                            <div className="flex items-center">
                                <div className="mr-4 p-3 rounded-full bg-purple-100 text-purple-500">
                                    <HiUserGroup className="text-xl" />
                                </div>
                                <span className="text-3xl font-bold">
                                    {dashboardData?.promedioAsistencia || 0}%
                                </span>
                            </div>
                        )}
                    </div>
                </AdaptableCard>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                {/* Gráfico: Eventos por Estado */}
                <Card className="lg:col-span-1 mb-6">
                    <div className="p-4">
                        <h4 className="mb-4 text-lg font-semibold">
                            Eventos por Estado
                        </h4>
                        {loading ? (
                            <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
                        ) : (
                            <div className="h-80">
                                {/* Renderizado condicional mejorado para el Chart */}
                                {series.length > 0 && labels.length > 0 ? (
                                    <Chart
                                        type="donut"
                                        series={series}
                                        height={300}
                                        customOptions={{
                                            labels: labels,
                                            legend: {
                                                show: true,
                                                position: 'bottom',
                                            },
                                            plotOptions: {
                                                pie: {
                                                    donut: {
                                                        labels: {
                                                            show: true,
                                                            total: {
                                                                show: true,
                                                                label: 'Total',
                                                                formatter:
                                                                    function () {
                                                                        return series
                                                                            .reduce(
                                                                                (
                                                                                    a,
                                                                                    b,
                                                                                ) =>
                                                                                    a +
                                                                                    b,
                                                                                0,
                                                                            )
                                                                            .toString()
                                                                    },
                                                            },
                                                        },
                                                    },
                                                },
                                            },
                                        }}
                                    />
                                ) : (
                                    <div className="flex items-center justify-center h-full">
                                        <p className="text-gray-500">
                                            No hay datos de estado de eventos
                                            para mostrar.
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </Card>

                {/* Gráfico: Eventos por Departamento */}
                <Card className="lg:col-span-2 mb-6">
                    <div className="p-4">
                        <h4 className="mb-4 text-lg font-semibold">
                            Eventos por Departamento
                        </h4>
                        {loading ? (
                            <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
                        ) : (
                            <div className="h-80">
                                {barChartCustomOptions && dashboardData ? (
                                    <Chart
                                        type="bar"
                                        series={[
                                            {
                                                name: 'Eventos',
                                data: dashboardData.eventosPorDepartamento.map(
                                                    (item) => item.count,
                                                ),
                                            },
                                        ]}
                                        customOptions={barChartCustomOptions}
                                        height={300}
                                    />
                                ) : (
                                    <div className="flex items-center justify-center h-full">
                                        <p className="text-gray-500">
                                            No hay datos de departamento para
                                            mostrar.
                                        </p>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </Card>
            </div>
        </Container>
    )
}

export default DashboardView
